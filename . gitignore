# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info
.installed.cfg
*.egg
*.pyc


# Virtual Environment
venv/
env/
ENV/
.env
.venv

# IDE
.idea/
.vscode/
*.swp
*.swo
.DS_Store

# Project specific
output/
*.log
logs/
.coverage
htmlcov/
.pytest_cache/
.mypy_cache/

# Jupyter Notebook
.ipynb_checkpoints
*.ipynb

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg