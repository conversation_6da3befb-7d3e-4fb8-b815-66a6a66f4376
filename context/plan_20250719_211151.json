{"tasks": [{"id": "t0_search_shops_grocery_stores", "type": "primitive", "name": "Search for grocery stores near given location", "orchestration_action": "search_shops", "parameters": {"location": "5th avenue, Austin, TX", "query": "grocery store"}, "preconditions": []}, {"id": "t1_rank_stores", "type": "primitive", "name": "Rank stores at least 5 km away", "orchestration_action": "rank_data", "parameters": {"prompt": "From the provided store search results in 'results_of_t0_search_shops_grocery_stores', identify all stores that are at least 5 km away from the origin address (5th avenue, Austin, TX). Then rank these stores first by highest user rating (if available) and second by shortest distance (with a minimum distance threshold of 5 km). Return the ranked list with distance and rating details.", "input_data": "results_of_t0_search_shops_grocery_stores", "criteria": {"distance_min_km": 5, "rating": "desc", "distance": "asc"}}, "preconditions": ["t0_search_shops_grocery_stores completed"]}, {"id": "t2_select_stores", "type": "primitive", "name": "Select top stores from ranked list", "orchestration_action": "select_data", "parameters": {"prompt": "From the ranked list of stores in 'results_of_t1_rank_stores', select up to 3 stores that offer a full grocery selection (e.g., supermarkets or hypermarkets) and have the best combined score for rating and distance.", "options": "results_of_t1_rank_stores.data.ranked_items", "criteria": {"store_type": "supermarket", "min_rank_score": 60}, "topk": 3, "explain": true}, "preconditions": ["t1_rank_stores completed"]}, {"id": "t3_global_search_canned_tomatoes", "type": "primitive", "name": "Search for canned tomatoes availability", "orchestration_action": "global_search", "parameters": {"query": "canned tomatoes near 5th avenue Austin TX grocery store"}, "preconditions": []}, {"id": "t4_global_search_shrimps", "type": "primitive", "name": "Search for shrimps availability", "orchestration_action": "global_search", "parameters": {"query": "shrimps near 5th avenue Austin TX grocery store"}, "preconditions": []}, {"id": "t5_global_search_steaks", "type": "primitive", "name": "Search for steaks availability", "orchestration_action": "global_search", "parameters": {"query": "steaks near 5th avenue Austin TX grocery store"}, "preconditions": []}, {"id": "t6_global_search_potatoes", "type": "primitive", "name": "Search for potatoes availability", "orchestration_action": "global_search", "parameters": {"query": "potatoes near 5th avenue Austin TX grocery store"}, "preconditions": []}, {"id": "t7_global_search_butter", "type": "primitive", "name": "Search for butter availability", "orchestration_action": "global_search", "parameters": {"query": "butter near 5th avenue Austin TX grocery store"}, "preconditions": []}, {"id": "t8_evaluate_store_item_coverage", "type": "primitive", "name": "Evaluate stores for item coverage", "orchestration_action": "evaluate_data", "parameters": {"input_data": {"selected_stores": "results_of_t2_select_stores.data.selected_options", "canned_tomatoes_search": "results_of_t3_global_search_canned_tomatoes", "shrimps_search": "results_of_t4_global_search_shrimps", "steaks_search": "results_of_t5_global_search_steaks", "potatoes_search": "results_of_t6_global_search_potatoes", "butter_search": "results_of_t7_global_search_butter"}, "criteria": {"item_coverage": "maximize", "distance_min_km": 5}}, "preconditions": ["t2_select_stores completed", "t3_global_search_canned_tomatoes completed", "t4_global_search_shrimps completed", "t5_global_search_steaks completed", "t6_global_search_potatoes completed", "t7_global_search_butter completed"]}, {"id": "t9_generate_final_answer", "type": "primitive", "name": "Generate final shopping recommendation", "orchestration_action": "generate_final_answers", "parameters": {"processed_data": "results_of_t8_evaluate_store_item_coverage", "original_question": "can you do my grocery shopping list where i can them, the stores need to be at least 5km from my location", "format": "markdown"}, "preconditions": ["t8_evaluate_store_item_coverage completed"]}]}