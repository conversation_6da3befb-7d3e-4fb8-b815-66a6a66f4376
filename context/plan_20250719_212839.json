{"tasks": [{"id": "t1_google_walmart_search", "type": "primitive", "name": "Locate Walmart stores near Austin at driving distance", "orchestration_action": "Google Maps", "parameters": {"origin": "5th ave, Austin, TX", "destination": "Walmart near Austin, TX", "mode": "drive"}, "preconditions": []}, {"id": "t2_google_target_search", "type": "primitive", "name": "Locate Target stores near Austin at driving distance", "orchestration_action": "Google Maps", "parameters": {"origin": "5th ave, Austin, TX", "destination": "Target near Austin, TX", "mode": "drive"}, "preconditions": []}, {"id": "t3_google_costco_search", "type": "primitive", "name": "Locate Costco stores near Austin at driving distance", "orchestration_action": "Google Maps", "parameters": {"origin": "5th ave, Austin, TX", "destination": "Costco near Austin, TX", "mode": "drive"}, "preconditions": []}, {"id": "t4_google_aldi_search", "type": "primitive", "name": "Locate Aldi stores near Austin at driving distance", "orchestration_action": "Google Maps", "parameters": {"origin": "5th ave, Austin, TX", "destination": "Aldi near Austin, TX", "mode": "drive"}, "preconditions": []}, {"id": "t5_rank_stores_distance", "type": "primitive", "name": "Filter and rank stores at least 5 km away", "orchestration_action": "rank_data", "parameters": {"prompt": "From the provided Google Maps route information for multiple store brands, extract each store's name, address, and driving distance in kilometers from 5th ave, Austin, TX. Filter out any stores that are less than 5 km away. Rank the remaining stores by ascending distance and score them based on proximity (lower is better) and assumed availability of the requested grocery items (shrimps, canned tomatoes, steak, potatoes, butter). Provide a ranked list of the top stores.", "input_data": ["results_of_t1_google_walmart_search", "results_of_t2_google_target_search", "results_of_t3_google_costco_search", "results_of_t4_google_aldi_search"], "criteria": {"min_distance_km": 5, "sort_by": "distance_asc"}}, "preconditions": ["t1_google_walmart_search completed", "t2_google_target_search completed", "t3_google_costco_search completed", "t4_google_aldi_search completed"]}, {"id": "t6_generate_final_answer", "type": "primitive", "name": "Generate final grocery shopping answer", "orchestration_action": "generate_final_answers", "parameters": {"processed_data": "results_of_t5_rank_stores_distance", "original_question": "can you do my grocery shopping list where i can them, the stores need to be at least 5km from my location", "format": "markdown"}, "preconditions": ["t5_rank_stores_distance completed"]}]}