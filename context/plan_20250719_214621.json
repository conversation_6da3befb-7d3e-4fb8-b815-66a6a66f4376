{"tasks": [{"id": "t1_walmart_location", "type": "primitive", "name": "Find Walmart stores within 5 km of 5th Ave Austin TX", "orchestration_action": "Google Maps", "parameters": {"origin": "5th Ave, Austin, TX", "search_query": "Walmart", "radius_km": "5"}, "preconditions": []}, {"id": "t2_target_location", "type": "primitive", "name": "Find Target stores within 5 km of 5th Ave Austin TX", "orchestration_action": "Google Maps", "parameters": {"origin": "5th Ave, Austin, TX", "search_query": "Target", "radius_km": "5"}, "preconditions": []}, {"id": "t3_costco_location", "type": "primitive", "name": "Find Costco stores within 5 km of 5th Ave Austin TX", "orchestration_action": "Google Maps", "parameters": {"origin": "5th Ave, Austin, TX", "search_query": "Costco", "radius_km": "5"}, "preconditions": []}, {"id": "t4_aldi_location", "type": "primitive", "name": "Find Aldi stores within 5 km of 5th Ave Austin TX", "orchestration_action": "Google Maps", "parameters": {"origin": "5th Ave, Austin, TX", "search_query": "<PERSON><PERSON>", "radius_km": "5"}, "preconditions": []}, {"id": "t5_lidl_location", "type": "primitive", "name": "Find Lidl stores within 5 km of 5th Ave Austin TX", "orchestration_action": "Google Maps", "parameters": {"origin": "5th Ave, Austin, TX", "search_query": "Lidl", "radius_km": "5"}, "preconditions": []}, {"id": "t6_walmart_shrimp", "type": "primitive", "name": "Search Walmart for shrimp", "orchestration_action": "Walmart", "parameters": {"search_query": "shrimp"}, "preconditions": ["t1_walmart_location completed"]}, {"id": "t7_walmart_steak", "type": "primitive", "name": "Search Walmart for steak", "orchestration_action": "Walmart", "parameters": {"search_query": "steak"}, "preconditions": ["t1_walmart_location completed"]}, {"id": "t8_walmart_tomatoes", "type": "primitive", "name": "Search Walmart for tomatoes", "orchestration_action": "Walmart", "parameters": {"search_query": "tomatoes"}, "preconditions": ["t1_walmart_location completed"]}, {"id": "t9_walmart_potatoes", "type": "primitive", "name": "Search Walmart for potatoes", "orchestration_action": "Walmart", "parameters": {"search_query": "potatoes"}, "preconditions": ["t1_walmart_location completed"]}, {"id": "t10_walmart_butter", "type": "primitive", "name": "Search Walmart for butter", "orchestration_action": "Walmart", "parameters": {"search_query": "butter"}, "preconditions": ["t1_walmart_location completed"]}, {"id": "t11_target_shrimp", "type": "primitive", "name": "Search Target for shrimp", "orchestration_action": "Target", "parameters": {"search_query": "shrimp"}, "preconditions": ["t2_target_location completed"]}, {"id": "t12_target_steak", "type": "primitive", "name": "Search Target for steak", "orchestration_action": "Target", "parameters": {"search_query": "steak"}, "preconditions": ["t2_target_location completed"]}, {"id": "t13_target_tomatoes", "type": "primitive", "name": "Search Target for tomatoes", "orchestration_action": "Target", "parameters": {"search_query": "tomatoes"}, "preconditions": ["t2_target_location completed"]}, {"id": "t14_target_potatoes", "type": "primitive", "name": "Search Target for potatoes", "orchestration_action": "Target", "parameters": {"search_query": "potatoes"}, "preconditions": ["t2_target_location completed"]}, {"id": "t15_target_butter", "type": "primitive", "name": "Search Target for butter", "orchestration_action": "Target", "parameters": {"search_query": "butter"}, "preconditions": ["t2_target_location completed"]}, {"id": "t16_costco_shrimp", "type": "primitive", "name": "Search Costco for shrimp", "orchestration_action": "Costco", "parameters": {"search_query": "shrimp"}, "preconditions": ["t3_costco_location completed"]}, {"id": "t17_costco_steak", "type": "primitive", "name": "Search Costco for steak", "orchestration_action": "Costco", "parameters": {"search_query": "steak"}, "preconditions": ["t3_costco_location completed"]}, {"id": "t18_costco_tomatoes", "type": "primitive", "name": "Search Costco for tomatoes", "orchestration_action": "Costco", "parameters": {"search_query": "tomatoes"}, "preconditions": ["t3_costco_location completed"]}, {"id": "t19_costco_potatoes", "type": "primitive", "name": "Search Costco for potatoes", "orchestration_action": "Costco", "parameters": {"search_query": "potatoes"}, "preconditions": ["t3_costco_location completed"]}, {"id": "t20_costco_butter", "type": "primitive", "name": "Search Costco for butter", "orchestration_action": "Costco", "parameters": {"search_query": "butter"}, "preconditions": ["t3_costco_location completed"]}, {"id": "t21_aldi_shrimp", "type": "primitive", "name": "Search Aldi for shrimp", "orchestration_action": "<PERSON><PERSON>", "parameters": {"search_query": "shrimp"}, "preconditions": ["t4_aldi_location completed"]}, {"id": "t22_aldi_steak", "type": "primitive", "name": "Search Aldi for steak", "orchestration_action": "<PERSON><PERSON>", "parameters": {"search_query": "steak"}, "preconditions": ["t4_aldi_location completed"]}, {"id": "t23_aldi_tomatoes", "type": "primitive", "name": "Search Aldi for tomatoes", "orchestration_action": "<PERSON><PERSON>", "parameters": {"search_query": "tomatoes"}, "preconditions": ["t4_aldi_location completed"]}, {"id": "t24_aldi_potatoes", "type": "primitive", "name": "Search Aldi for potatoes", "orchestration_action": "<PERSON><PERSON>", "parameters": {"search_query": "potatoes"}, "preconditions": ["t4_aldi_location completed"]}, {"id": "t25_aldi_butter", "type": "primitive", "name": "Search Aldi for butter", "orchestration_action": "<PERSON><PERSON>", "parameters": {"search_query": "butter"}, "preconditions": ["t4_aldi_location completed"]}, {"id": "t26_lidl_shrimp", "type": "primitive", "name": "Search Lidl for shrimp", "orchestration_action": "Lidl", "parameters": {"search_query": "shrimp"}, "preconditions": ["t5_lidl_location completed"]}, {"id": "t27_lidl_steak", "type": "primitive", "name": "Search Lidl for steak", "orchestration_action": "Lidl", "parameters": {"search_query": "steak"}, "preconditions": ["t5_lidl_location completed"]}, {"id": "t28_lidl_tomatoes", "type": "primitive", "name": "Search Lidl for tomatoes", "orchestration_action": "Lidl", "parameters": {"search_query": "tomatoes"}, "preconditions": ["t5_lidl_location completed"]}, {"id": "t29_lidl_potatoes", "type": "primitive", "name": "Search Lidl for potatoes", "orchestration_action": "Lidl", "parameters": {"search_query": "potatoes"}, "preconditions": ["t5_lidl_location completed"]}, {"id": "t30_lidl_butter", "type": "primitive", "name": "Search Lidl for butter", "orchestration_action": "Lidl", "parameters": {"search_query": "butter"}, "preconditions": ["t5_lidl_location completed"]}, {"id": "t31_amazon_shrimp", "type": "primitive", "name": "Search Amazon for shrimp", "orchestration_action": "Amazon", "parameters": {"search_query": "shrimp"}, "preconditions": []}, {"id": "t32_amazon_steak", "type": "primitive", "name": "Search Amazon for steak", "orchestration_action": "Amazon", "parameters": {"search_query": "steak"}, "preconditions": []}, {"id": "t33_amazon_tomatoes", "type": "primitive", "name": "Search Amazon for tomatoes", "orchestration_action": "Amazon", "parameters": {"search_query": "tomatoes"}, "preconditions": []}, {"id": "t34_amazon_potatoes", "type": "primitive", "name": "Search Amazon for potatoes", "orchestration_action": "Amazon", "parameters": {"search_query": "potatoes"}, "preconditions": []}, {"id": "t35_amazon_butter", "type": "primitive", "name": "Search Amazon for butter", "orchestration_action": "Amazon", "parameters": {"search_query": "butter"}, "preconditions": []}, {"id": "t36_evaluate_availability", "type": "primitive", "name": "Evaluate which stores cover the grocery list within 5 km", "orchestration_action": "evaluate_data", "parameters": {"input_data": ["results_of_t6_walmart_shrimp", "results_of_t7_walmart_steak", "results_of_t8_walmart_tomatoes", "results_of_t9_walmart_potatoes", "results_of_t10_walmart_butter", "results_of_t11_target_shrimp", "results_of_t12_target_steak", "results_of_t13_target_tomatoes", "results_of_t14_target_potatoes", "results_of_t15_target_butter", "results_of_t16_costco_shrimp", "results_of_t17_costco_steak", "results_of_t18_costco_tomatoes", "results_of_t19_costco_potatoes", "results_of_t20_costco_butter", "results_of_t21_aldi_shrimp", "results_of_t22_aldi_steak", "results_of_t23_aldi_tomatoes", "results_of_t24_aldi_potatoes", "results_of_t25_aldi_butter", "results_of_t26_lidl_shrimp", "results_of_t27_lidl_steak", "results_of_t28_lidl_tomatoes", "results_of_t29_lidl_potatoes", "results_of_t30_lidl_butter", "results_of_t31_amazon_shrimp", "results_of_t32_amazon_steak", "results_of_t33_amazon_tomatoes", "results_of_t34_amazon_potatoes", "results_of_t35_amazon_butter"], "criteria": {"goal": "Find the store or combination of stores within 5 km that covers all required grocery items with minimum number of stops"}}, "preconditions": ["t6_walmart_shrimp completed", "t7_walmart_steak completed", "t8_walmart_tomatoes completed", "t9_walmart_potatoes completed", "t10_walmart_butter completed", "t11_target_shrimp completed", "t12_target_steak completed", "t13_target_tomatoes completed", "t14_target_potatoes completed", "t15_target_butter completed", "t16_costco_shrimp completed", "t17_costco_steak completed", "t18_costco_tomatoes completed", "t19_costco_potatoes completed", "t20_costco_butter completed", "t21_aldi_shrimp completed", "t22_aldi_steak completed", "t23_aldi_tomatoes completed", "t24_aldi_potatoes completed", "t25_aldi_butter completed", "t26_lidl_shrimp completed", "t27_lidl_steak completed", "t28_lidl_tomatoes completed", "t29_lidl_potatoes completed", "t30_lidl_butter completed", "t31_amazon_shrimp completed", "t32_amazon_steak completed", "t33_amazon_tomatoes completed", "t34_amazon_potatoes completed", "t35_amazon_butter completed"]}, {"id": "t37_generate_final_answer", "type": "primitive", "name": "Generate final shopping plan answer for the user", "orchestration_action": "generate_final_answers", "parameters": {"processed_data": "results_of_t36_evaluate_availability", "original_question": "Grocery list: shrimps, steak, tomatoes, potatoes, butter. Need stores within 5 km of 5th Ave, Austin, TX.", "format": "markdown"}, "preconditions": ["t36_evaluate_availability completed"]}]}