"""
Dynamic Task Generator module for GaiaV2.

This module handles the generation of tasks based on previous results.
It supports iteration over collections of results and conditional task generation.
"""

import os
import json
import logging
import re
import uuid
from typing import Dict, List, Any, Set, Optional, Union, Tuple

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger('gaiav2.execution.dynamic_task_generator')

class DynamicTaskGenerator:
    """
    Generates tasks dynamically based on previous results.
    
    This class provides methods for generating tasks based on previous results,
    including support for iteration over collections of results and conditional
    task generation.
    """
    
    def __init__(self, results: Dict[str, Any], action_map: Dict[str, Dict[str, Any]]):
        """
        Initialize the dynamic task generator.
        
        Args:
            results: Dictionary of task results
            action_map: Mapping from task IDs to actions and parameters
        """
        self.results = results
        self.action_map = action_map
        self.task_templates = {}  # Store task templates
        self.generated_tasks = {}  # Store generated tasks
        
    def register_template(self, template_id: str, template: Dict[str, Any]):
        """
        Register a task template.
        
        Args:
            template_id: ID of the template
            template: Template definition
        """
        self.task_templates[template_id] = template
        logger.info(f"Registered task template: {template_id}")
        
    def generate_tasks_from_template(self, template_id: str, context: Dict[str, Any] = None) -> List[str]:
        """
        Generate tasks from a template.
        
        Args:
            template_id: ID of the template to use
            context: Context for template instantiation (optional)
            
        Returns:
            List of generated task IDs
        """
        if template_id not in self.task_templates:
            logger.error(f"Template not found: {template_id}")
            return []
            
        template = self.task_templates[template_id]
        template_type = template.get("type", "simple")
        
        if template_type == "for_each":
            return self._generate_for_each_tasks(template, context)
        elif template_type == "conditional":
            return self._generate_conditional_tasks(template, context)
        else:
            return self._generate_simple_tasks(template, context)
            
    def _generate_simple_tasks(self, template: Dict[str, Any], context: Dict[str, Any] = None) -> List[str]:
        """
        Generate a single task from a template.
        
        Args:
            template: Template definition
            context: Context for template instantiation (optional)
            
        Returns:
            List containing the generated task ID
        """
        # Generate a unique task ID
        task_id = self._generate_task_id(template.get("id_prefix", "t"))
        
        # Create the task from the template
        task = {
            "action": template.get("action"),
            "parameters": self._process_template_parameters(template.get("parameters", {}), context),
            "name": self._process_template_string(template.get("name", f"Task {task_id}"), context)
        }
        
        # Add the task to the action map
        self.action_map[task_id] = task
        
        # Add to generated tasks
        self.generated_tasks[task_id] = {
            "template_id": template.get("id"),
            "context": context
        }
        
        logger.info(f"Generated task {task_id} from template")
        
        return [task_id]
        
    def _generate_for_each_tasks(self, template: Dict[str, Any], context: Dict[str, Any] = None) -> List[str]:
        """
        Generate tasks for each item in a collection.
        
        Args:
            template: Template definition
            context: Context for template instantiation (optional)
            
        Returns:
            List of generated task IDs
        """
        # Get the collection to iterate over
        collection_ref = template.get("collection")
        if not collection_ref:
            logger.error("No collection specified for for_each template")
            return []
            
        # Resolve the collection reference
        collection = self._resolve_reference(collection_ref, context)
        if not collection or not isinstance(collection, (list, dict)):
            logger.error(f"Invalid collection for for_each template: {collection}")
            return []
            
        # Convert dict to list of (key, value) tuples if necessary
        items = collection.items() if isinstance(collection, dict) else enumerate(collection)
        
        # Generate a task for each item
        generated_task_ids = []
        
        for idx, item in items:
            # Create item context
            item_context = context.copy() if context else {}
            item_context["item"] = item
            item_context["index"] = idx
            
            # Generate a unique task ID with index
            id_prefix = template.get("id_prefix", "t")
            task_id = self._generate_task_id(f"{id_prefix}_{idx}")
            
            # Create the task from the template
            task = {
                "action": template.get("action"),
                "parameters": self._process_template_parameters(template.get("parameters", {}), item_context),
                "name": self._process_template_string(template.get("name", f"Task {task_id}"), item_context)
            }
            
            # Add the task to the action map
            self.action_map[task_id] = task
            
            # Add to generated tasks
            self.generated_tasks[task_id] = {
                "template_id": template.get("id"),
                "context": item_context
            }
            
            generated_task_ids.append(task_id)
            
        logger.info(f"Generated {len(generated_task_ids)} tasks from for_each template")
        
        return generated_task_ids
        
    def _generate_conditional_tasks(self, template: Dict[str, Any], context: Dict[str, Any] = None) -> List[str]:
        """
        Generate tasks conditionally based on a condition.
        
        Args:
            template: Template definition
            context: Context for template instantiation (optional)
            
        Returns:
            List of generated task IDs
        """
        # Get the condition
        condition_ref = template.get("condition")
        if not condition_ref:
            logger.error("No condition specified for conditional template")
            return []
            
        # Resolve the condition reference
        condition_value = self._resolve_reference(condition_ref, context)
        
        # Check if the condition is truthy
        if condition_value:
            # Use the "then" template
            then_template = template.get("then")
            if not then_template:
                logger.error("No 'then' template specified for conditional template")
                return []
                
            # Generate tasks from the "then" template
            return self._generate_simple_tasks(then_template, context)
        else:
            # Use the "else" template if provided
            else_template = template.get("else")
            if not else_template:
                logger.info("No 'else' template specified for conditional template, skipping")
                return []
                
            # Generate tasks from the "else" template
            return self._generate_simple_tasks(else_template, context)
            
    def _generate_task_id(self, prefix: str = "t") -> str:
        """
        Generate a unique task ID.
        
        Args:
            prefix: Prefix for the task ID
            
        Returns:
            Unique task ID
        """
        # Generate a unique ID using UUID
        unique_id = uuid.uuid4().hex[:6]
        
        # Create the task ID
        task_id = f"{prefix}_{unique_id}"
        
        # Ensure it doesn't conflict with existing task IDs
        while task_id in self.action_map:
            unique_id = uuid.uuid4().hex[:6]
            task_id = f"{prefix}_{unique_id}"
            
        return task_id
        
    def _process_template_parameters(self, parameters: Dict[str, Any], context: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Process template parameters, replacing placeholders with values from context.
        
        Args:
            parameters: Template parameters
            context: Context for template instantiation (optional)
            
        Returns:
            Processed parameters
        """
        processed = {}
        
        for key, value in parameters.items():
            if isinstance(value, str):
                processed[key] = self._process_template_string(value, context)
            elif isinstance(value, dict):
                processed[key] = self._process_template_parameters(value, context)
            elif isinstance(value, list):
                processed[key] = [
                    self._process_template_parameters(item, context) if isinstance(item, dict)
                    else (self._process_template_string(item, context) if isinstance(item, str) else item)
                    for item in value
                ]
            else:
                processed[key] = value
                
        return processed
        
    def _process_template_string(self, template_str: str, context: Dict[str, Any] = None) -> str:
        """
        Process a template string, replacing placeholders with values from context.
        
        Args:
            template_str: Template string
            context: Context for template instantiation (optional)
            
        Returns:
            Processed string
        """
        if not isinstance(template_str, str):
            return template_str
            
        if not context:
            return template_str
            
        # Replace placeholders in the format ${variable}
        def replace_placeholder(match):
            placeholder = match.group(1)
            
            # Check if it's a reference to a task result
            if placeholder.startswith("results_of_") or placeholder.startswith("output_of_") or "_from_" in placeholder:
                return str(self._resolve_reference(placeholder))
                
            # Check if it's a reference to a context variable
            if placeholder in context:
                value = context[placeholder]
                return str(value) if not isinstance(value, (dict, list)) else json.dumps(value)
                
            # If not found, return the original placeholder
            return match.group(0)
            
        # Use regex to find and replace placeholders
        return re.sub(r'\${([^}]+)}', replace_placeholder, template_str)
        
    def _resolve_reference(self, reference: str, context: Dict[str, Any] = None) -> Any:
        """
        Resolve a reference to a task result or context variable.
        
        Args:
            reference: Reference string
            context: Context for template instantiation (optional)
            
        Returns:
            Resolved value
        """
        # Import the reference resolver here to avoid circular imports
        from gaiav2.utils.reference_resolver import ReferenceResolver
        
        # Create a reference resolver with the current results
        resolver = ReferenceResolver(self.results)
        
        # Resolve the reference
        resolved_value = resolver.resolve_reference(reference)
        
        # If the reference wasn't resolved and we have a context, check there
        if resolved_value is reference and context:
            # Check if it's a reference to a context variable
            if reference in context:
                return context[reference]
                
            # Check if it's a nested reference using dot notation
            if "." in reference:
                parts = reference.split(".", 1)
                if parts[0] in context:
                    # Use the reference resolver to handle nested access
                    dummy_results = {"results_of_dummy": context[parts[0]]}
                    dummy_resolver = ReferenceResolver(dummy_results)
                    dummy_reference = f"results_of_dummy.{parts[1]}"
                    resolved_dummy = dummy_resolver.resolve_reference(dummy_reference)
                    
                    if resolved_dummy is not dummy_reference:
                        return resolved_dummy
        
        return resolved_value
