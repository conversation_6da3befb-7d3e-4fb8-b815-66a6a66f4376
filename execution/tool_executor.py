"""
Tool Executor module for GaiaV2.

This module handles the execution of tasks using the tools library.
It executes tasks based on a dependency graph.
"""

import os
import json
import asyncio
import logging
import datetime
from typing import Dict, List, Any, Set, Optional, Union, Tuple
import importlib
import sys
import re
import functools  # For functools.partial

# Ensure correct path for importing 'tools'
# This assumes 'tools' is in the root directory
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from tools import (
    # Search tools
    serp, amazon, flights, hotels, maps, youtube, images, scholar, web,
    aldi, cars, costco, etsy, home_depot, immoweb, lidl, nike, target, ubereats,
    walmart, zalando, zara, hm, tjmaxx, ikea, carrefour, zillow, mediamarkt,
    manomano, leroymerlin, louisvuitton,
    # Level 2 tools
    generate_final_answers, generate_structured_data, rank_data, evaluate_data, select_data
)

# from gaiav2.execution.dynamic_task_generator import DynamicTaskGenerator # If needed later

logger = logging.getLogger('gaiav2.execution.tool_executor')


class ToolExecutor:
    """
    Executes tasks using the tools library based on the task dependency graph.
    Handles primitives-only plans.
    """

    def __init__(self, plan: Dict[str, List[Dict[str, Any]]], user_query: Optional[str] = None):
        if not isinstance(plan, dict) or "tasks" not in plan or not isinstance(plan["tasks"], list):
            logger.error(f"Invalid plan format received by ToolExecutor: {str(plan)[:500]}")
            raise ValueError("Invalid plan format. Must be a dict with a 'tasks' list.")

        self.plan_tasks: List[Dict[str, Any]] = plan["tasks"]
        self.user_query = user_query
        self.results: Dict[str, Any] = {}

        self.context_dir = "context"
        self.tasks_dir = os.path.join(self.context_dir, "tasks")
        self._ensure_context_dirs()

        self.action_map: Dict[str, Dict[str, Any]] = {}
        for task_data in self.plan_tasks:
            task_id = task_data.get("id")
            if not task_id:
                logger.warning(f"Task data missing 'id', skipping: {str(task_data)[:200]}")
                continue
            self.action_map[task_id] = {
                "action": task_data.get("orchestration_action"),
                "parameters": task_data.get("parameters", {}),
                "name": task_data.get("name", task_id),
                "preconditions": task_data.get("preconditions", [])
            }

        self.dynamic_task_generator = None  # Placeholder for now
        self.task_templates: Dict[str, Any] = {}
        self.dynamic_tasks: Set[str] = set()

        self.task_dependencies: Dict[str, List[str]] = {task_id: [] for task_id in self.action_map}
        self.task_dependents: Dict[str, List[str]] = {task_id: [] for task_id in self.action_map}
        self.completed_tasks: Set[str] = set()
        self.failed_tasks: Set[str] = set()
        self.running_tasks: Set[str] = set()

        self._build_dependency_graph()
        try:
            self._validate_dependency_graph()
        except ValueError as e:
            logger.error(f"ToolExecutor initialization failed due to invalid dependency graph: {e}")
            raise

    def _ensure_context_dirs(self):
        if not os.path.exists(self.context_dir):
            os.makedirs(self.context_dir)
        if not os.path.exists(self.tasks_dir):
            os.makedirs(self.tasks_dir)

    def _build_dependency_graph(self):
        all_task_ids = set(self.action_map.keys())

        for task_id, task_info in self.action_map.items():
            # 1. Dependencies from "preconditions" field
            for precond_str in task_info.get("preconditions", []):
                dep_task_id = precond_str.split()[0]  # Assumes "task_id completed" or "task_id"
                if dep_task_id in all_task_ids:
                    if dep_task_id not in self.task_dependencies[task_id]:
                        self.task_dependencies[task_id].append(dep_task_id)
                    if task_id not in self.task_dependents[dep_task_id]:
                        self.task_dependents[dep_task_id].append(task_id)
                else:  # Corrected indentation for this else block
                    logger.info(
                        f"Task '{task_id}' precondition '{precond_str}' refers to "
                        f"unknown task ID '{dep_task_id}'. Will not form graph dependency."
                    )

            # 2. Dependencies from parameter values (e.g., "results_of_another_task")
            parameters = task_info.get("parameters", {})
            for param_value in self._iter_all_parameter_values(parameters):
                if isinstance(param_value, str):
                    # Regex to find results_of_TASK_ID, output_of_TASK_ID, or FIELD_from_TASK_ID
                    match = re.match(r"(?:results_of_|output_of_|[^_]+_from_)([a-zA-Z0-9_\\-]+)", param_value)
                    if match:
                        dep_task_id = match.group(1)
                        if dep_task_id in all_task_ids:
                            if dep_task_id != task_id:  # Avoid self-dependency from parameters
                                if dep_task_id not in self.task_dependencies[task_id]:
                                    self.task_dependencies[task_id].append(dep_task_id)
                                    if task_id not in self.task_dependents[dep_task_id]:
                                        self.task_dependents[dep_task_id].append(task_id)
                        else:
                            # Only log warning if it's a clear "results_of_" or "output_of_" pattern
                            if param_value.startswith("results_of_") or param_value.startswith("output_of_"):
                                logger.warning(
                                    f"Task '{task_id}' parameter '{param_value}' refers to "
                                    f"unknown task '{dep_task_id}'."
                                )

        logger.info("Dependency graph built:")
        for task_id, deps in self.task_dependencies.items():
            if deps:
                logger.info(f"  Task '{task_id}' depends on: {deps}")
            else:
                logger.info(f"  Task '{task_id}' has no dependencies.")

    def _iter_all_parameter_values(self, parameters: Any) -> Any:
        if isinstance(parameters, dict):
            for v in parameters.values():
                yield from self._iter_all_parameter_values(v)
        elif isinstance(parameters, list):
            for item in parameters:
                yield from self._iter_all_parameter_values(item)
        elif isinstance(parameters, str):
            yield parameters

    def _validate_dependency_graph(self):
        visited_nodes = set()
        recursion_stack = set()
        for node_id in self.action_map.keys():
            if node_id not in visited_nodes:
                if self._dfs_cycle_check(node_id, visited_nodes, recursion_stack):
                    # Error already logged by _dfs_cycle_check
                    raise ValueError(f"Cyclic dependency detected involving task: {node_id}")
        logger.info("Dependency graph validated: No cycles found.")

    def _dfs_cycle_check(self, node_id: str, visited_nodes: Set[str], recursion_stack: Set[str]) -> bool:
        visited_nodes.add(node_id)
        recursion_stack.add(node_id)

        for prerequisite_task_id in self.task_dependencies.get(node_id, []):
            if prerequisite_task_id not in visited_nodes:
                if self._dfs_cycle_check(prerequisite_task_id, visited_nodes, recursion_stack):
                    return True
            elif prerequisite_task_id in recursion_stack:
                logger.error(
                    f"Cycle detected: Task {node_id} depends on {prerequisite_task_id}, "
                    f"which is already in recursion stack: {list(recursion_stack)}"
                )
                return True  # Corrected indentation

        recursion_stack.remove(node_id)
        return False # Corrected indentation

    def get_ready_tasks(self) -> Set[str]:
        ready = set()
        for task_id in self.action_map.keys():
            if task_id in self.completed_tasks or task_id in self.failed_tasks or task_id in self.running_tasks:
                continue

            dependencies_met = True
            current_task_dependencies = self.task_dependencies.get(task_id, [])
            if not current_task_dependencies:
                ready.add(task_id)
                continue

            for dep_id in current_task_dependencies:
                if dep_id not in self.completed_tasks:
                    dependencies_met = False
                    break
                if dep_id in self.failed_tasks:
                    if task_id not in self.failed_tasks:
                        logger.warning(
                            f"Task {task_id} cannot run because dependency {dep_id} failed. "
                            f"Marking {task_id} as failed."
                        )
                        self.failed_tasks.add(task_id)
                    dependencies_met = False
                    break

            if dependencies_met:
                ready.add(task_id)
        return ready

    async def execute_plan(self) -> Dict[str, Any]:
        logger.info("Starting plan execution...")
        all_task_ids = set(self.action_map.keys())

        while len(self.completed_tasks) + len(self.failed_tasks) < len(all_task_ids):
            ready_to_run_tasks = self.get_ready_tasks()

            if not ready_to_run_tasks and not self.running_tasks:
                remaining_tasks = all_task_ids - self.completed_tasks - self.failed_tasks
                if not remaining_tasks:
                    logger.info("All tasks accounted for (completed or failed). Exiting execution loop.")
                    break # Corrected indentation

                all_remaining_blocked_by_actual_failure = True
                for rem_task_id in remaining_tasks:
                    is_blocked = False
                    for dep_id in self.task_dependencies.get(rem_task_id, []):
                        if dep_id in self.failed_tasks:
                            is_blocked = True
                            if rem_task_id not in self.failed_tasks:
                                logger.warning(
                                    f"Marking task {rem_task_id} as failed "
                                    f"(blocked by failed dependency {dep_id})."
                                )
                                self.failed_tasks.add(rem_task_id)
                            break
                    if not is_blocked:  # Found a task not blocked by a *known* failure
                        all_remaining_blocked_by_actual_failure = False
                        break # Corrected indentation: this break is for the inner for loop

                if all_remaining_blocked_by_actual_failure:
                    logger.warning(
                        f"Execution halted. All {len(remaining_tasks)} remaining tasks "
                        f"are blocked by upstream failures."
                    )
                else:
                    logger.error(
                        f"Deadlock: No tasks ready, none running, but {len(remaining_tasks)} tasks pending "
                        f"and not directly blocked by failed dependencies."
                    )
                    for pt_id in remaining_tasks:
                        if pt_id not in self.failed_tasks:  # Mark as failed due to deadlock
                            unmet_deps = [
                                dep for dep in self.task_dependencies.get(pt_id, [])
                                if dep not in self.completed_tasks and dep not in self.failed_tasks
                            ]
                            logger.error(f"  Task {pt_id} (deadlocked) unmet non-failed dependencies: {unmet_deps}")
                            self.failed_tasks.add(pt_id)
                break  # Exit while loop on deadlock or full blockage

            elif not ready_to_run_tasks and self.running_tasks:
                logger.info(f"Waiting for {len(self.running_tasks)} running tasks: {list(self.running_tasks)}. Ready: 0.")
                await asyncio.sleep(0.1)
                continue # Continue to next iteration of while loop

            execution_promises = []
            tasks_submitted_this_round = []
            for task_id in ready_to_run_tasks:
                if task_id not in self.running_tasks:
                    logger.info(f"Adding task '{task_id}' to current execution batch.")
                    self.running_tasks.add(task_id)
                    execution_promises.append(self.execute_task(task_id))
                    tasks_submitted_this_round.append(task_id)

            if not execution_promises and not self.running_tasks and \
               (len(self.completed_tasks) + len(self.failed_tasks) < len(all_task_ids)):
                logger.error("Execution loop anomaly: No tasks submitted, none running, plan incomplete. Breaking.")
                for rt_id in (all_task_ids - self.completed_tasks - self.failed_tasks):
                    self.failed_tasks.add(rt_id)
                break # Corrected indentation

            if execution_promises:
                logger.info(f"Executing batch of {len(execution_promises)} tasks: {tasks_submitted_this_round}")
                results_this_batch = await asyncio.gather(*execution_promises, return_exceptions=True)

                for task_id_done, result_or_exception_tuple in zip(tasks_submitted_this_round, results_this_batch):
                    if task_id_done not in self.running_tasks:
                        logger.warning(f"Task {task_id_done} was already removed from running_tasks upon gather completion.")
                    self.running_tasks.remove(task_id_done)
                    if isinstance(result_or_exception_tuple, Exception):
                        logger.error(f"Task '{task_id_done}' execution failed: {result_or_exception_tuple}", exc_info=True)
                        self.failed_tasks.add(task_id_done)
                    else:
                        actual_result, processed_params = result_or_exception_tuple
                        logger.info(f"Task '{task_id_done}' completed successfully.")
                        self.save_task_result(task_id_done, actual_result, processed_params)
                        self.completed_tasks.add(task_id_done)
            elif not self.running_tasks and (len(self.completed_tasks) + len(self.failed_tasks) < len(all_task_ids)):
                logger.info("No tasks submitted, no tasks running. Waiting for conditions or deadlock resolution.")
                await asyncio.sleep(0.5)

        total_accounted = len(self.completed_tasks) + len(self.failed_tasks)
        if total_accounted == len(all_task_ids):
            logger.info(
                f"All {len(all_task_ids)} tasks accounted for. "
                f"Completed: {len(self.completed_tasks)}, Failed: {len(self.failed_tasks)}."
            )
        else:
            logger.warning(
                f"Plan execution loop finished. Accounted: {total_accounted}, "
                f"Total in plan: {len(all_task_ids)}."
            )
            unaccounted_tasks = all_task_ids - self.completed_tasks - self.failed_tasks
            if unaccounted_tasks:
                logger.warning(f"Unaccounted tasks: {unaccounted_tasks}. Marking them as failed.")
                for task_id in unaccounted_tasks:
                    self.failed_tasks.add(task_id)

        self.save_execution_summary()
        return self.results

    async def execute_task(self, task_id: str) -> Tuple[Any, Dict[str, Any]]:
        task_info = self.action_map.get(task_id)
        if not task_info:
            logger.error(f"Task ID '{task_id}' not found in action map for execution.")
            self.failed_tasks.add(task_id)
            raise ValueError(f"Task ID '{task_id}' not found in action_map.")

        action = task_info.get("action")
        parameters = task_info.get("parameters", {})
        task_name = task_info.get("name", task_id)

        if not action:
            logger.error(f"Task '{task_id}' ({task_name}) has no 'orchestration_action' defined.")
            self.failed_tasks.add(task_id)
            raise ValueError(f"Task '{task_id}' has no action.")

        logger.info(f"Executing task '{task_id}': {task_name} (Action: {action})")

        processed_params = {}
        try:
            processed_params = self._process_parameters(parameters) # Corrected indentation
            logger.debug(f"Task '{task_id}' processed parameters: {processed_params}")
            result = await self._execute_tool_function(action, processed_params)
            logger.info(f"Task '{task_id}' ({task_name}) raw result: Type {type(result)}")
            return result, processed_params
        except Exception as e:
            logger.error(f"Error during execution of task '{task_id}' (Action: {action}): {e}", exc_info=True)
            if task_id not in self.failed_tasks:
                self.failed_tasks.add(task_id)
            raise

    def _resolve_path_from_object(self, data_object: Any, path_str: str) -> Any:
        if not path_str:
            return data_object

        current = data_object
        # Regex to find . or [index] or ['key'] or [\"key\"]
        # Corrected regex: single backslashes for regex special chars in raw string
        path_segments_raw = re.split(r'''\.(?![^\[]*\])|(\[(?:\d+|'[^']+'|"[^"]+")\])''', path_str)
        path_segments = []

        for part in path_segments_raw:
            if not part:
                continue
            # Corrected regexes: single backslashes for \d, \[, \]
            idx_match = re.fullmatch(r'\[(\d+)\]', part)
            key_match_single = re.fullmatch(r"\['([^']+)'\]", part)
            key_match_double = re.fullmatch(r'\["([^"]+)"\]', part)

            if idx_match:
                path_segments.append(int(idx_match.group(1)))
            elif key_match_single:
                path_segments.append(key_match_single.group(1))
            elif key_match_double:
                path_segments.append(key_match_double.group(1))
            else:
                path_segments.append(part)

        if not path_segments and path_str and (path_str.isalnum() or ('_' in path_str)):
            path_segments = [path_str]
        elif not path_segments and path_str:
            logger.warning(f"Could not parse path segments from path_str: '{path_str}'.")
            return None  # Cannot resolve if path is unparseable

        for key_or_index in path_segments:
            try:
                if isinstance(key_or_index, str):
                    if isinstance(current, dict):
                        current = current[key_or_index]
                    elif hasattr(current, key_or_index):
                        current = getattr(current, key_or_index)
                    else:
                        logger.warning(
                            f"Cannot access key/attribute '{key_or_index}' in path '{path_str}'. "
                            f"Current object type: {type(current)}."
                        )
                        return None
                elif isinstance(key_or_index, int):
                    current = current[key_or_index]
            except (KeyError, IndexError, TypeError, AttributeError) as e:
                logger.warning(
                    f"Error accessing '{key_or_index}' in path '{path_str}': {e}. "
                    f"Current object type: {type(current)}."
                )
                return None
        return current

    def _process_parameters(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        processed = {}
        if not isinstance(parameters, dict):
            logger.warning(f"Parameters object is not a dict: {parameters}. Returning as is.")
            return parameters  # type: ignore

        for key, value in parameters.items():
            if isinstance(value, str):
                processed[key] = self._process_string_reference(value)
            elif isinstance(value, list):
                processed[key] = [self._process_list_item(item) for item in value]
            elif isinstance(value, dict):
                processed[key] = self._process_parameters(value)
            else:
                processed[key] = value
        return processed

    def _process_string_reference(self, value: str) -> Any:
        # Regex to find all occurrences of references like results_of_TASK_ID.path or output_of_TASK_ID[0].field
        # It captures the type (results_of_ or output_of_), the task_id, and the optional path.
        # It also handles the legacy FIELD_from_TASK_ID format separately for now.

        # Pattern for new style references: results_of_TASK_ID.path or output_of_TASK_ID.path
        # Captures: (1: type like "results_of_"), (2: task_id), (3: path including leading dot if present)
        pattern_new = r"(results_of_|output_of_)([a-zA-Z0-9_\\-]+)((?:\.[\w\-]+|\[\d+\]|\[\'[\w\-]+\'\]|\[\"[\w\-]+\"\])*)"
        # Pattern for legacy style: FIELD_from_TASK_ID
        # Captures: (1: field_key), (2: task_id)
        pattern_legacy = r"([a-zA-Z0-9_]+)_from_([a-zA-Z0-9_\\-]+)"

        def replace_match(match_obj):
            # Determine if it's a new style or legacy style match based on which group matched
            if match_obj.group(1) and (match_obj.group(1).startswith("results_of_") or match_obj.group(1).startswith("output_of_")):
                # New style: results_of_TASK_ID.path or output_of_TASK_ID.path
                dep_task_id = match_obj.group(2)
                path_str = match_obj.group(3)

                if dep_task_id not in self.results:
                    logger.error(f"Dependency task '{dep_task_id}' result not found for parameter value part '{match_obj.group(0)}'.")
                    raise ValueError(f"Result for dependency task '{dep_task_id}' not found.")
                task_result_obj = self.results[dep_task_id]

                # Remove leading dot from path_str if it exists, as _resolve_path_from_object might expect it clean or handle it
                if path_str and path_str.startswith('.'):
                    path_str_cleaned = path_str[1:]
                else:
                    path_str_cleaned = path_str

                if not path_str_cleaned: # If path is empty, return the whole task result object
                    resolved_value = task_result_obj
                else:
                    resolved_value = self._resolve_path_from_object(task_result_obj, path_str_cleaned)

                # Ensure the resolved value is a string for substitution in the larger string
                return str(resolved_value) if resolved_value is not None else ""

            elif match_obj.group(4) and match_obj.group(5): # Check groups for legacy pattern
                # Legacy style: FIELD_from_TASK_ID
                field_key = match_obj.group(4)
                dep_task_id = match_obj.group(5)

                if dep_task_id not in self.results:
                    logger.error(f"Legacy ref: Dependency task '{dep_task_id}' result not found for '{match_obj.group(0)}'.")
                    raise ValueError(f"Result for legacy dependency task '{dep_task_id}' not found.")
                task_result_obj = self.results[dep_task_id]

                resolved_value = None
                if isinstance(task_result_obj, dict):
                    if field_key not in task_result_obj:
                        logger.warning(
                            f"Legacy field_key '{field_key}' not in task '{dep_task_id}' results for '{match_obj.group(0)}'. "
                            f"Keys: {list(task_result_obj.keys())}"
                        )
                    resolved_value = task_result_obj.get(field_key)
                else:
                    if not hasattr(task_result_obj, field_key):
                        logger.warning(
                            f"Legacy field_key '{field_key}' not attr on task '{dep_task_id}' "
                            f"results (type: {type(task_result_obj)}) for '{match_obj.group(0)}'."
                        )
                    resolved_value = getattr(task_result_obj, field_key, None)
                return str(resolved_value) if resolved_value is not None else ""

            # Should not happen if regex is correct
            return match_obj.group(0)

        # Combine patterns for re.sub: new style OR legacy style
        # We use non-capturing groups (?:...) for the OR logic at the top level if needed,
        # but here we make them distinct capturing groups to tell them apart in replace_match.
        # The combined pattern needs to capture distinctive parts for new vs legacy.
        # New pattern captures 3 groups. Legacy captures 2 groups.
        # Let's adjust combined_pattern to ensure groups don't overlap ambiguously.
        # New: (group1)(group2)(group3) | Legacy: (group4)(group5)
        # Group 1: results_of_ or output_of_
        # Group 2: TASK_ID for new style
        # Group 3: .path for new style
        # Group 4: FIELD for legacy style
        # Group 5: TASK_ID for legacy style
        combined_pattern = f"({pattern_new})|({pattern_legacy})"

        # Check if the entire string is JUST a reference (no surrounding text)
        # This is important to return non-string types if the reference itself is a dict/list etc.
        full_match_new = re.fullmatch(pattern_new, value)
        if full_match_new:
            # It's a new style full match, handle as before to preserve type
            dep_task_id = full_match_new.group(2)
            path_str = full_match_new.group(3)
            if dep_task_id not in self.results:
                logger.error(f"Dependency task '{dep_task_id}' result not found for parameter value '{value}'.")
                raise ValueError(f"Result for dependency task '{dep_task_id}' not found.")
            task_result_obj = self.results[dep_task_id]
            if path_str and path_str.startswith('.'):
                path_str = path_str[1:]
            return self._resolve_path_from_object(task_result_obj, path_str) if path_str else task_result_obj

        full_match_legacy = re.fullmatch(pattern_legacy, value)
        if full_match_legacy:
            # It's a legacy style full match
            field_key = full_match_legacy.group(1)
            dep_task_id = full_match_legacy.group(2)
            if dep_task_id not in self.results:
                logger.error(f"Legacy ref: Dependency task '{dep_task_id}' result not found for '{value}'.")
                raise ValueError(f"Result for legacy dependency task '{dep_task_id}' not found.")
            task_result_obj = self.results[dep_task_id]
            if isinstance(task_result_obj, dict):
                return task_result_obj.get(field_key)
            else:
                return getattr(task_result_obj, field_key, None)

        # If not a full match, perform substitution for embedded references.
        # The `replace_match` function is designed to work with the groups from `combined_pattern`.
        # We need to adjust `combined_pattern` and `replace_match` to correctly map groups.
        # Let's make combined_pattern simpler by having it OR two patterns, and replace_match inspects which part of OR matched.
        # New combined pattern with named groups for clarity in replace_match:
        named_pattern_new = r"(?P<new_style_ref>(?P<new_type>results_of_|output_of_)(?P<new_task_id>[a-zA-Z0-9_\\-]+)(?P<new_path>(?:\.[\w\-]+|\[\d+\]|\[\'[\w\-]+\'\]|\[\"[\w\-]+\"\])*))"
        named_pattern_legacy = r"(?P<legacy_style_ref>(?P<legacy_field_key>[a-zA-Z0-9_]+)_from_(?P<legacy_task_id>[a-zA-Z0-9_\\-]+))"
        combined_named_pattern = f"(?:{named_pattern_new}|{named_pattern_legacy})"

        def replace_named_match(match_obj):
            if match_obj.group("new_style_ref"):
                dep_task_id = match_obj.group("new_task_id")
                path_str = match_obj.group("new_path")
                # ... (logic as in original replace_match for new style) ...
                if dep_task_id not in self.results:
                    logger.error(f"Dependency task '{dep_task_id}' result not found for parameter value part '{match_obj.group(0)}'.")
                    raise ValueError(f"Result for dependency task '{dep_task_id}' not found.")
                task_result_obj = self.results[dep_task_id]
                if path_str and path_str.startswith('.'):
                    path_str_cleaned = path_str[1:]
                else:
                    path_str_cleaned = path_str
                resolved_value = self._resolve_path_from_object(task_result_obj, path_str_cleaned) if path_str_cleaned else task_result_obj
                return str(resolved_value) if resolved_value is not None else ""
            elif match_obj.group("legacy_style_ref"):
                field_key = match_obj.group("legacy_field_key")
                dep_task_id = match_obj.group("legacy_task_id")
                # ... (logic as in original replace_match for legacy style) ...
                if dep_task_id not in self.results:
                    logger.error(f"Legacy ref: Dependency task '{dep_task_id}' result not found for '{match_obj.group(0)}'.")
                    raise ValueError(f"Result for legacy dependency task '{dep_task_id}' not found.")
                task_result_obj = self.results[dep_task_id]
                resolved_value = None
                if isinstance(task_result_obj, dict):
                    resolved_value = task_result_obj.get(field_key)
                else:
                    resolved_value = getattr(task_result_obj, field_key, None)
                return str(resolved_value) if resolved_value is not None else ""
            return match_obj.group(0) # Fallback, should not be reached

        # Perform substitution using re.sub with the combined named pattern
        # This will replace all occurrences of either pattern in the string.
        processed_value = re.sub(combined_named_pattern, replace_named_match, value)
        return processed_value

    def _process_list_item(self, item: Any) -> Any:
        if isinstance(item, str):
            return self._process_string_reference(item) # Corrected indentation
        elif isinstance(item, dict):
            return self._process_parameters(item)
        elif isinstance(item, list):
            return [self._process_list_item(sub_item) for sub_item in item]
        return item

    def _transform_parameters_for_tool(self, action: str, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """
        Transform and validate parameters for specific tools based on their expected interface.
        """
        processed_params = parameters.copy()

        # Handle flight search parameters
        if action in ["search_flight", "search_flights", "Google Flights"]:
            # Ensure required parameters are present
            if "from_airport" not in processed_params and "origin" in processed_params:
                processed_params["from_airport"] = processed_params.pop("origin")
            if "to_airport" not in processed_params and "destination" in processed_params:
                processed_params["to_airport"] = processed_params.pop("destination")

            # Set defaults for optional parameters
            processed_params.setdefault("trip_type", "one-way")
            processed_params.setdefault("adults", 1)
            processed_params.setdefault("children", 0)
            processed_params.setdefault("infants_in_seat", 0)
            processed_params.setdefault("infants_on_lap", 0)

        # Handle hotel search parameters
        elif action in ["search_hotel", "search_hotels", "Google Hotels"]:
            # Set defaults for optional parameters
            processed_params.setdefault("adults", 1)
            processed_params.setdefault("children", 0)
            processed_params.setdefault("currency", "USD")

        # Handle maps/places search parameters
        elif action in ["search_restaurant", "search_activity", "search_shops", "Google Maps"]:
            # Set defaults for maps searches
            processed_params.setdefault("radius", 5000)
            processed_params.setdefault("language", "en")

            # Set place_type based on action
            if action == "search_restaurant":
                processed_params.setdefault("place_type", "restaurant")
            elif action == "search_activity":
                processed_params.setdefault("place_type", "tourist_attraction")
            elif action == "search_shops":
                processed_params.setdefault("place_type", "store")

        # Handle e-commerce search parameters
        elif action in ["search_products", "search_amazon", "Amazon"] or any(store in action.lower() for store in ["walmart", "target", "costco", "etsy", "nike", "zara"]):
            # Set defaults for product searches
            processed_params.setdefault("max_results", 10)

            # Add API keys from environment if not provided
            if "zyte_api_key" not in processed_params:
                import os
                zyte_key = os.environ.get("ZYTE_API_KEY")
                if zyte_key:
                    processed_params["zyte_api_key"] = zyte_key

        # Handle SERP/web search parameters
        elif action in ["global_search", "search_web", "SERP", "Web Search"]:
            processed_params.setdefault("max_results", 10)

            # Add API keys from environment if not provided
            if "brave_api_key" not in processed_params:
                import os
                brave_key = os.environ.get("BRAVE_API_KEY")
                if brave_key:
                    processed_params["brave_api_key"] = brave_key
            if "zyte_api_key" not in processed_params:
                import os
                zyte_key = os.environ.get("ZYTE_API_KEY")
                if zyte_key:
                    processed_params["zyte_api_key"] = zyte_key

        # Handle YouTube search parameters
        elif action in ["search_youtube", "YouTube"]:
            processed_params.setdefault("max_results", 10)

            # Add API key from environment if not provided
            if "api_key" not in processed_params:
                import os
                youtube_key = os.environ.get("YOUTUBE_API_KEY")
                if youtube_key:
                    processed_params["api_key"] = youtube_key

        # Handle image search parameters
        elif action in ["search_images", "Google Images"]:
            processed_params.setdefault("max_results", 10)

            # Add API key from environment if not provided
            if "api_key" not in processed_params:
                import os
                serpapi_key = os.environ.get("SERPAPI_API_KEY")
                if serpapi_key:
                    processed_params["api_key"] = serpapi_key

        # Handle scholar search parameters
        elif action in ["search_scientific", "Google Scholar"]:
            processed_params.setdefault("max_results", 10)

            # Add API key from environment if not provided
            if "api_key" not in processed_params:
                import os
                serpapi_key = os.environ.get("SERPAPI_API_KEY")
                if serpapi_key:
                    processed_params["api_key"] = serpapi_key

        # Handle grocery store searches
        elif any(store in action.lower() for store in ["aldi", "lidl", "carrefour"]):
            # Set default country if not provided
            if "country" not in processed_params and "countries" not in processed_params:
                processed_params["country"] = "UK"  # Default country

        # Handle UberEats search parameters
        elif action in ["search_ubereats", "Uber Eats"]:
            # Add API keys from environment if not provided
            if "zyte_api_key" not in processed_params:
                import os
                zyte_key = os.environ.get("ZYTE_API_KEY")
                if zyte_key:
                    processed_params["zyte_api_key"] = zyte_key
            if "maps_api_key" not in processed_params:
                import os
                maps_key = os.environ.get("GOOGLE_MAPS_API_KEY")
                if maps_key:
                    processed_params["maps_api_key"] = maps_key

        return processed_params

    async def _execute_tool_function(self, action: str, parameters: Dict[str, Any]) -> Any:
        tool_function_map = {
            # Core search actions
            "search_flight": flights.search,
            "search_hotel": hotels.search,
            "search_routes": maps.get_directions,
            "search_restaurant": maps.search_places,
            "search_activity": maps.search_places,
            "search_shops": maps.search_places,
            "search_products": amazon.search,
            "global_search": serp.search,
            "search_scientific": scholar.search,

            # E-commerce and retail search actions
            "search_amazon": amazon.search,
            "search_walmart": walmart.search,
            "search_target": target.search,
            "search_costco": costco.search,
            "search_etsy": etsy.search,
            "search_home_depot": home_depot.search,
            "search_ikea": ikea.search,
            "search_tjmaxx": tjmaxx.search,

            # Fashion and clothing search actions
            "search_nike": nike.search,
            "search_zara": zara.search,
            "search_hm": hm.search,
            "search_zalando": zalando.search,
            "search_louisvuitton": louisvuitton.search,

            # Grocery and food search actions
            "search_aldi": aldi.search,
            "search_lidl": lidl.search,
            "search_carrefour": carrefour.search,
            "search_ubereats": ubereats.search,

            # Real estate and property search actions
            "search_immoweb": immoweb.search,
            "search_zillow": zillow.search,

            # Electronics and technology search actions
            "search_mediamarkt": mediamarkt.search,

            # DIY and home improvement search actions
            "search_manomano": manomano.search,
            "search_leroymerlin": leroymerlin.search,

            # Automotive search actions
            "search_cars": cars.search,

            # Media and content search actions
            "search_youtube": youtube.search,
            "search_images": images.search,
            "search_web": web.search,

            # Level 2 processing actions
            "generate_final_answers": generate_final_answers.generate_final_answers,
            "generate_structured_data": generate_structured_data.generate_structured_data,
            "rank_data": rank_data.rank_data,
            "evaluate_data": evaluate_data.evaluate_data,
            "select_data": select_data.select_data,
        }

        # Handle intelligent routing for generic search actions
        if action == "search_products":
            # Route to specific e-commerce platform based on parameters or context
            platform = parameters.get("platform", "").lower()
            if platform == "amazon":
                action = "search_amazon"
            elif platform == "walmart":
                action = "search_walmart"
            elif platform == "target":
                action = "search_target"
            elif platform == "costco":
                action = "search_costco"
            elif platform == "etsy":
                action = "search_etsy"
            elif platform == "home_depot":
                action = "search_home_depot"
            elif platform == "ikea":
                action = "search_ikea"
            elif platform == "tjmaxx":
                action = "search_tjmaxx"
            # Default to Amazon if no platform specified
            elif not platform:
                action = "search_amazon"

        elif action == "search_fashion":
            # Route to specific fashion platform based on parameters
            platform = parameters.get("platform", "").lower()
            if platform == "nike":
                action = "search_nike"
            elif platform == "zara":
                action = "search_zara"
            elif platform == "hm" or platform == "h&m":
                action = "search_hm"
            elif platform == "zalando":
                action = "search_zalando"
            elif platform == "louisvuitton" or platform == "louis_vuitton":
                action = "search_louisvuitton"
            # Default to Zara if no platform specified
            elif not platform:
                action = "search_zara"

        elif action == "search_groceries":
            # Route to specific grocery platform based on parameters
            platform = parameters.get("platform", "").lower()
            country = parameters.get("country", "").lower()
            if platform == "aldi":
                action = "search_aldi"
            elif platform == "lidl":
                action = "search_lidl"
            elif platform == "carrefour":
                action = "search_carrefour"
            # Default based on country if no platform specified
            elif country in ["uk", "united kingdom", "gb"]:
                action = "search_aldi"
            elif country in ["fr", "france", "be", "belgium"]:
                action = "search_carrefour"
            else:
                action = "search_aldi"  # Default

        elif action == "search_real_estate":
            # Route to specific real estate platform based on parameters
            platform = parameters.get("platform", "").lower()
            country = parameters.get("country", "").lower()
            if platform == "zillow":
                action = "search_zillow"
            elif platform == "immoweb":
                action = "search_immoweb"
            # Default based on country
            elif country in ["us", "usa", "united states"]:
                action = "search_zillow"
            elif country in ["be", "belgium"]:
                action = "search_immoweb"
            else:
                action = "search_zillow"  # Default

        # Handle tool name to function mapping for tools selected by tool selector
        if not tool_function_map.get(action):
            # Try to map tool names from tool selector to actual functions
            tool_name_mapping = {
                "Google Maps": maps.search_places,
                "Lidl": lidl.search,
                "Aldi": aldi.search,
                "Carrefour": carrefour.search,
                "Walmart": walmart.search,
                "Costco": costco.search,
                "Target": target.search,
                "Amazon": amazon.search,
                "MediaMarkt": mediamarkt.search,
                "Home Depot": home_depot.search,
                "Leroy Merlin": leroymerlin.search,
                "ManoMano": manomano.search,
                "Zara": zara.search,
                "H&M": hm.search,
                "TJ Maxx": tjmaxx.search,
                "Zalando": zalando.search,
                "Nike": nike.search,
                "Louis Vuitton": louisvuitton.search,
                "Uber Eats": ubereats.search,
                "Zillow": zillow.search,
                "Immoweb": immoweb.search,
                "Google Flights": flights.search,
                "Google Hotels": hotels.search,
                "YouTube": youtube.search,
                "Google Images": images.search,
                "Google Scholar": scholar.search,
                "SERP": serp.search,
                "Web Search": web.search,
                "Cars": cars.search,
                "Etsy": etsy.search,
                "IKEA": ikea.search,
            }

            # Try exact match first
            if action in tool_name_mapping:
                tool_func = tool_name_mapping[action]
            # Try case-insensitive match
            else:
                for tool_name, func in tool_name_mapping.items():
                    if action.lower() == tool_name.lower():
                        tool_func = func
                        break
        else:
            tool_func = tool_function_map.get(action)

        # Pre-check for critical parameters for specific actions
        if action == "search_hotel":
            destination = parameters.get("destination")
            if not destination: # Checks for None or empty string
                logger.error(
                    f"Action '{action}' is missing required 'destination' parameter or it resolved to None/empty. "
                    f"Processed parameters: {parameters}"
                )
                raise ValueError(f"Action '{action}' missing or has invalid 'destination' parameter after processing.")

        if not tool_func:
            parts = action.split('.')
            if len(parts) == 2:
                module_name, func_name = parts[0], parts[1]
                try:
                    # Assuming 'tools' submodules are already imported and available in globals()
                    # or accessible via importlib if a more dynamic approach is needed.
                    module = globals().get(module_name)
                    if module and hasattr(module, func_name):
                        tool_func = getattr(module, func_name)
                    else: # Corrected indentation
                        raise NotImplementedError(f"Tool action '{action}' (module.func) not found or module not imported.")
                except Exception as e:
                    logger.error(f"Error resolving module.function for action '{action}': {e}")
                    raise NotImplementedError(f"Could not load tool for action '{action}': {e}")
            else: # Corrected indentation
                raise NotImplementedError(f"Tool action '{action}' is not directly mapped or in 'module.func' format.")

        if not tool_func:  # Should be caught by logic above, but final safeguard
            raise NotImplementedError(f"Tool function for action '{action}' could not be resolved.")

        # Parameter transformation and validation for specific tools
        processed_parameters = self._transform_parameters_for_tool(action, parameters)

        logger.debug(f"Calling tool '{tool_func.__name__}' from action '{action}' with params: {processed_parameters}")
        if asyncio.iscoroutinefunction(tool_func):
            return await tool_func(**processed_parameters)
        else: # Corrected indentation
            loop = asyncio.get_running_loop()
            logger.warning(f"Tool function {action} ({tool_func.__name__}) is synchronous. Running in executor.")
            partial_func = functools.partial(tool_func, **processed_parameters)
            return await loop.run_in_executor(None, partial_func)

    def save_task_result(self, task_id: str, result: Any, parameters: Dict[str, Any]):
        self.results[task_id] = result
        self._ensure_context_dirs()
        filename = os.path.join(self.tasks_dir, f"{task_id}_result.json")

        data_to_save = {
            "task_id": task_id,
            "input_parameters": parameters,
            "output_result": result
        }

        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(data_to_save, f, indent=2, ensure_ascii=False)
            logger.info(f"Saved result and input parameters for task '{task_id}' to {filename}")
        except TypeError:
            logger.warning(
                f"Result or parameters for task '{task_id}' (result type: {type(result)}, "
                f"params type: {type(parameters)}) are not fully JSON serializable. "
                "Saving string representation."
            )
            try:
                serializable_params = {}
                for k, v in parameters.items():
                    try:
                        json.dumps(v)  # Test serializability
                        serializable_params[k] = v
                    except TypeError:
                        serializable_params[k] = f"[Unserializable value of type {type(v)}]: {str(v)}"

                serializable_result = {}
                try:
                    json.dumps(result)  # Test serializability
                    serializable_result = result
                except TypeError:
                    serializable_result = {"result_type": str(type(result)), "result_as_string": str(result)}

                serializable_data_to_save = {
                    "task_id": task_id,
                    "input_parameters": serializable_params,
                    "output_result_fallback": serializable_result
                }
                with open(filename, 'w', encoding='utf-8') as f:
                    json.dump(serializable_data_to_save, f, indent=2, ensure_ascii=False)
                logger.info(f"Saved string/fallback representation for task '{task_id}' to {filename}")
            except Exception as e_ser:
                logger.error(f"Could not save result for task '{task_id}' even as string representation: {e_ser}")
                txt_filename = os.path.join(self.tasks_dir, f"{task_id}_result_UNSERIALIZABLE.txt")
                with open(txt_filename, 'w', encoding='utf-8') as f_txt:
                    f_txt.write(f"Task: {task_id}\\nInput Parameters:\\n{str(parameters)}\\n\\n"
                                f"Output Result Type: {type(result)}\\n\\n{str(result)}")
                logger.info(f"Saved raw string result and params for task '{task_id}' to {txt_filename}")

    def save_final_answer(self, result: Any, main_query_for_filename: Optional[str] = None):
        self._ensure_context_dirs()
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        query_prefix = re.sub(r'\\W+', '_', main_query_for_filename[:30]) if main_query_for_filename else "final"

        filename_md = os.path.join(self.context_dir, f"final_answer_{query_prefix}_{timestamp}.md")
        filename_json = os.path.join(self.context_dir, f"final_answer_{query_prefix}_{timestamp}.json")

        final_answer_content = result.get("answer", str(result)) if isinstance(result, dict) else str(result)

        try:
            if isinstance(final_answer_content, str):
                with open(filename_md, 'w', encoding='utf-8') as f_md:
                    f_md.write(final_answer_content)
                logger.info(f"Final answer (markdown/text) saved to {filename_md}")
                try:
                    json_data = json.loads(final_answer_content) # Check if content is valid JSON string
                    with open(filename_json, 'w', encoding='utf-8') as f_json:
                        json.dump(json_data, f_json, indent=2, ensure_ascii=False)
                    logger.info(f"Final answer (parsed as JSON string) also saved to {filename_json}")
                except json.JSONDecodeError:
                    # Content was string but not valid JSON, that's fine.
                    pass

            # If original result was a dict, save it as JSON regardless of 'answer' field
            if isinstance(result, dict):
                with open(filename_json, 'w', encoding='utf-8') as f_json:
                    json.dump(result, f_json, indent=2, ensure_ascii=False)
                logger.info(f"Original final answer (dictionary) also saved to {filename_json}")

        except Exception as e:
            logger.error(f"Could not save final answer content: {e}")
            fallback_filename = os.path.join(self.context_dir, f"final_answer_UNSERIALIZED_{timestamp}.txt")
            with open(fallback_filename, 'w', encoding='utf-8') as f_txt:
                f_txt.write(str(result))
            logger.info(f"Final answer saved as raw string to {fallback_filename}")

    def save_execution_summary(self):
        self._ensure_context_dirs()
        summary_filename = os.path.join(
            self.context_dir, f"execution_summary_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        )

        results_preview_serializable = {}
        for tid, res in self.results.items():
            if tid in self.completed_tasks:
                try:
                    res_str = str(res)
                    results_preview_serializable[tid] = res_str[:200] + "..." if len(res_str) > 200 else res_str
                except Exception:
                    results_preview_serializable[tid] = f"[Unserializable result of type {type(res)}]"

        summary_data = {
            "user_query": self.user_query,
            "total_tasks_in_plan": len(self.action_map),
            "completed_tasks_count": len(self.completed_tasks),
            "failed_tasks_count": len(self.failed_tasks),
            "completed_task_ids": sorted(list(self.completed_tasks)),
            "failed_task_ids": sorted(list(self.failed_tasks)),
            "results_preview": results_preview_serializable
        }
        try:
            with open(summary_filename, 'w', encoding='utf-8') as f:
                json.dump(summary_data, f, indent=2, ensure_ascii=False)
            logger.info(f"Execution summary saved to {summary_filename}")
        except Exception as e:
            logger.error(f"Failed to save execution summary: {e}")

    def register_task_template(self, template_id: str, template: Dict[str, Any]):
        logger.info(f"Registering task template: {template_id}")
        self.task_templates[template_id] = template

    def generate_tasks_from_template(
        self, template_id: str, context: Dict[str, Any] = None, dependencies: List[str] = None
    ) -> List[str]:
        logger.info(f"Generating tasks from template: {template_id}")
        # Placeholder: Actual dynamic task generation logic would be complex.
        logger.warning("Dynamic task generation from template is currently a placeholder and does not add new tasks.")
        return []
