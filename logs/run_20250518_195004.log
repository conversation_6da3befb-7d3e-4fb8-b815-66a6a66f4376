2025-05-18 19:50:10,294 - root - INFO - Step 1 (Iteration 1/5): Decomposing query...
2025-05-18 19:50:10,294 - root - INFO - DECOMPOSE | Sending planner prompt to LLM...
2025-05-18 19:50:10,294 - root - INFO - Auto-switching provider from openai to openrouter for model google/gemini-2.5-pro-exp-03-25
2025-05-18 19:50:10,301 - root - INFO - OpenRouter provider initialized
2025-05-18 19:50:11,193 - httpx - INFO - HTTP Request: POST https://openrouter.ai/api/v1/chat/completions "HTTP/1.1 404 Not Found"
2025-05-18 19:50:11,197 - root - ERROR - Error generating content with OpenRouter: Error code: 404 - {'error': {'message': 'No endpoints found for google/gemini-2.5-pro-exp-03-25.', 'code': 404}, 'user_id': 'user_2vJq6t4AqBS0SJdZCuo8N8LFSLO'}
2025-05-18 19:50:11,197 - root - ERROR - DECOMPOSE | Unexpected error during query decomposition: Error code: 404 - {'error': {'message': 'No endpoints found for google/gemini-2.5-pro-exp-03-25.', 'code': 404}, 'user_id': 'user_2vJq6t4AqBS0SJdZCuo8N8LFSLO'}
2025-05-18 19:50:11,223 - root - ERROR - Traceback (most recent call last):
  File "/Users/<USER>/code/snipesearch/gaiav2/planners/QueryDecomposer.py", line 44, in decompose_query
    raw_content = await self.llm.generate(
  File "/Users/<USER>/code/snipesearch/gaiav2/llm/llm.py", line 250, in generate
    return await temp_llm.generate(
  File "/Users/<USER>/code/snipesearch/gaiav2/llm/llm.py", line 263, in generate
    return await self.provider.generate(
  File "/Users/<USER>/code/snipesearch/gaiav2/llm/openrouter_provider.py", line 151, in generate
    response = await self.client.chat.completions.create(**params)
  File "/Users/<USER>/code/snipesearch/.venv/lib/python3.9/site-packages/openai/resources/chat/completions/completions.py", line 2028, in create
    return await self._post(
  File "/Users/<USER>/code/snipesearch/.venv/lib/python3.9/site-packages/openai/_base_client.py", line 1742, in post
    return await self.request(cast_to, opts, stream=stream, stream_cls=stream_cls)
  File "/Users/<USER>/code/snipesearch/.venv/lib/python3.9/site-packages/openai/_base_client.py", line 1549, in request
    raise self._make_status_error_from_response(err.response) from None
openai.NotFoundError: Error code: 404 - {'error': {'message': 'No endpoints found for google/gemini-2.5-pro-exp-03-25.', 'code': 404}, 'user_id': 'user_2vJq6t4AqBS0SJdZCuo8N8LFSLO'}

2025-05-18 19:50:11,224 - root - INFO - Step 1 (Iteration 1): Decomposition resulted in a question.
