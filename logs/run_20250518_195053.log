2025-05-18 19:50:56,481 - root - INFO - Step 1 (Iteration 1/5): Decomposing query...
2025-05-18 19:50:56,481 - root - INFO - DECOMPOSE | Sending planner prompt to LLM...
2025-05-18 19:50:56,481 - root - INFO - Auto-switching provider from openai to openrouter for model google/gemini-2.5-pro-preview
2025-05-18 19:50:56,491 - root - INFO - OpenRouter provider initialized
2025-05-18 19:50:57,103 - httpx - INFO - HTTP Request: POST https://openrouter.ai/api/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-18 19:52:39,008 - root - ERROR - DECOMPOSE | Empty or failed response from LLM. Returning query as question.
2025-05-18 19:52:39,009 - root - INFO - Step 1 (Iteration 1): Decomposition resulted in a question.
