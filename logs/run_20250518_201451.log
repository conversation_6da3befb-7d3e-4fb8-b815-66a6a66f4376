2025-05-18 20:14:54,447 - root - INFO - Step 1 (Iteration 1/5): Decomposing query...
2025-05-18 20:14:54,447 - root - INFO - DECOMPOSE | Sending planner prompt to LLM...
2025-05-18 20:14:54,447 - root - INFO - Setting up parameters for thinking model: o3
2025-05-18 20:14:54,447 - root - INFO - Setting reasoning_effort=high for thinking model: o3
2025-05-18 20:14:54,448 - root - INFO - Using thinking model: o3
2025-05-18 20:14:54,448 - root - INFO - Setting reasoning_effort=high for model o3
2025-05-18 20:16:25,315 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-18 20:16:25,337 - root - INFO - DECOMPOSE | Raw response content received (first 500 chars): {
  "tasks": [
    {
      "id": "t1_search_cities",
      "type": "primitive",
      "name": "Search best cities to visit in Thailand",
      "orchestration_action": "global_search",
      "parameters": {
        "query": "best cities to visit in Thailand",
        "max_results": 20
      },
      "preconditions": []
    },
    {
      "id": "t2_rank_cities",
      "type": "primitive",
      "name": "Rank top cities in Thailand for tourism",
      "orchestration_action": "rank_data",
      "par...
2025-05-18 20:16:25,337 - root - INFO - DECOMPOSE | Successfully parsed raw response as JSON.
2025-05-18 20:16:25,337 - root - INFO - DECOMPOSE | Detected HTN-like plan structure with 'tasks' key.
2025-05-18 20:16:25,337 - root - INFO - DECOMPOSE | Successfully processed into primitives-only plan with 11 task(s).
2025-05-18 20:16:25,337 - root - INFO - Step 1 complete: Decomposition successful.
2025-05-18 20:16:25,340 - root - INFO - Plan saved to context/plan_20250518_201625.json
2025-05-18 20:16:25,344 - root - INFO - Step 2: Initializing WebPlanner and constructing graph
2025-05-18 20:16:25,346 - root - INFO - Step 2 complete: Graph constructed
2025-05-18 20:16:25,347 - root - INFO - Step 3: Plotting the plan graph
2025-05-18 20:16:25,356 - root - INFO - Using multipartite layout based on node depth, stretched horizontally.
2025-05-18 20:16:26,029 - root - INFO - Step 3 complete: Plan graph plotted
2025-05-18 20:16:26,029 - root - INFO - Step 4: Generating primitive execution schedule
2025-05-18 20:16:26,030 - root - INFO - Skipping legacy PrimitiveExecutor schedule/action_map generation as ToolExecutor will handle it.
2025-05-18 20:16:26,030 - root - INFO - Step 6: Executing all levels of tasks using the tools library
2025-05-18 20:16:26,030 - gaiav2.execution.tool_executor - INFO - Dependency graph built:
2025-05-18 20:16:26,030 - gaiav2.execution.tool_executor - INFO -   Task 't1_search_cities' has no dependencies.
2025-05-18 20:16:26,030 - gaiav2.execution.tool_executor - INFO -   Task 't2_rank_cities' depends on: ['t1_search_cities']
2025-05-18 20:16:26,030 - gaiav2.execution.tool_executor - INFO -   Task 't3_search_hotels_city1' depends on: ['t2_rank_cities']
2025-05-18 20:16:26,030 - gaiav2.execution.tool_executor - INFO -   Task 't4_search_hotels_city2' depends on: ['t2_rank_cities']
2025-05-18 20:16:26,030 - gaiav2.execution.tool_executor - INFO -   Task 't5_select_hotel_city1' depends on: ['t3_search_hotels_city1']
2025-05-18 20:16:26,031 - gaiav2.execution.tool_executor - INFO -   Task 't6_select_hotel_city2' depends on: ['t4_search_hotels_city2']
2025-05-18 20:16:26,031 - gaiav2.execution.tool_executor - INFO -   Task 't7_search_activities_city1' depends on: ['t2_rank_cities']
2025-05-18 20:16:26,031 - gaiav2.execution.tool_executor - INFO -   Task 't8_search_activities_city2' depends on: ['t2_rank_cities']
2025-05-18 20:16:26,034 - gaiav2.execution.tool_executor - INFO -   Task 't9_select_activity_city1' depends on: ['t7_search_activities_city1']
2025-05-18 20:16:26,036 - gaiav2.execution.tool_executor - INFO -   Task 't10_select_activity_city2' depends on: ['t8_search_activities_city2']
2025-05-18 20:16:26,037 - gaiav2.execution.tool_executor - INFO -   Task 't11_generate_final_answer' depends on: ['t5_select_hotel_city1', 't6_select_hotel_city2', 't9_select_activity_city1', 't10_select_activity_city2', 't2_rank_cities']
2025-05-18 20:16:26,038 - gaiav2.execution.tool_executor - INFO - Dependency graph validated: No cycles found.
2025-05-18 20:16:26,038 - root - INFO - Step 6.1: Registering task templates for dynamic task generation
2025-05-18 20:16:26,038 - gaiav2.execution.tool_executor - INFO - Registering task template: process_ranked_items
2025-05-18 20:16:26,038 - gaiav2.execution.tool_executor - INFO - Registering task template: process_selected_options
2025-05-18 20:16:26,038 - gaiav2.execution.tool_executor - INFO - Registering task template: conditional_high_score
2025-05-18 20:16:26,038 - root - INFO - Step 6.1 complete: Task templates registered
2025-05-18 20:34:20,728 - root - INFO - Step 6 skipped: User chose not to execute tasks.
