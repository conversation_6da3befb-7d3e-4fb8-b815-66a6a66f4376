2025-07-31 14:40:01,809 - root - INFO - Attempting to load plan from: ../test_plan/plan_20250511_193326.json
2025-07-31 14:40:01,809 - root - INFO - Successfully loaded plan from ../test_plan/plan_20250511_193326.json
2025-07-31 14:40:01,811 - root - INFO - Step 2: Initializing WebPlanner and constructing graph
2025-07-31 14:40:01,811 - root - INFO - Step 2 complete: Graph constructed
2025-07-31 14:40:01,811 - root - INFO - Step 3: Plotting the plan graph
2025-07-31 14:40:01,812 - root - INFO - Using multipartite layout based on node depth, stretched horizontally.
2025-07-31 14:40:02,718 - root - INFO - Step 3 complete: Plan graph plotted
2025-07-31 14:40:02,722 - root - INFO - Step 4: Generating primitive execution schedule
2025-07-31 14:40:02,722 - root - INFO - Skipping legacy PrimitiveExecutor schedule/action_map generation as ToolExecutor will handle it.
2025-07-31 14:40:02,722 - root - INFO - Step 6: Executing all levels of tasks using the tools library
2025-07-31 14:40:02,723 - gaiav2.execution.tool_executor - INFO - Dependency graph built:
2025-07-31 14:40:02,723 - gaiav2.execution.tool_executor - INFO -   Task 't1_search_cities' has no dependencies.
2025-07-31 14:40:02,724 - gaiav2.execution.tool_executor - INFO -   Task 't2_rank_cities' depends on: ['t1_search_cities']
2025-07-31 14:40:02,724 - gaiav2.execution.tool_executor - INFO -   Task 't3_search_hotels_city1' depends on: ['t2_rank_cities']
2025-07-31 14:40:02,724 - gaiav2.execution.tool_executor - INFO -   Task 't4_select_hotel_city1' depends on: ['t3_search_hotels_city1']
2025-07-31 14:40:02,724 - gaiav2.execution.tool_executor - INFO -   Task 't5_search_activity_city1' depends on: ['t4_select_hotel_city1']
2025-07-31 14:40:02,726 - gaiav2.execution.tool_executor - INFO -   Task 't6_select_activity_city1' depends on: ['t5_search_activity_city1']
2025-07-31 14:40:02,726 - gaiav2.execution.tool_executor - INFO -   Task 't7_search_hotels_city2' depends on: ['t2_rank_cities']
2025-07-31 14:40:02,726 - gaiav2.execution.tool_executor - INFO -   Task 't8_select_hotel_city2' depends on: ['t7_search_hotels_city2']
2025-07-31 14:40:02,731 - gaiav2.execution.tool_executor - INFO -   Task 't9_search_activity_city2' depends on: ['t8_select_hotel_city2']
2025-07-31 14:40:02,731 - gaiav2.execution.tool_executor - INFO -   Task 't10_select_activity_city2' depends on: ['t9_search_activity_city2']
2025-07-31 14:40:02,731 - gaiav2.execution.tool_executor - INFO -   Task 't11_search_hotels_city3' depends on: ['t2_rank_cities']
2025-07-31 14:40:02,731 - gaiav2.execution.tool_executor - INFO -   Task 't12_select_hotel_city3' depends on: ['t11_search_hotels_city3']
2025-07-31 14:40:02,731 - gaiav2.execution.tool_executor - INFO -   Task 't13_search_activity_city3' depends on: ['t12_select_hotel_city3']
2025-07-31 14:40:02,731 - gaiav2.execution.tool_executor - INFO -   Task 't14_select_activity_city3' depends on: ['t13_search_activity_city3']
2025-07-31 14:40:02,731 - gaiav2.execution.tool_executor - INFO -   Task 't15_search_hotels_city4' depends on: ['t2_rank_cities']
2025-07-31 14:40:02,733 - gaiav2.execution.tool_executor - INFO -   Task 't16_select_hotel_city4' depends on: ['t15_search_hotels_city4']
2025-07-31 14:40:02,737 - gaiav2.execution.tool_executor - INFO -   Task 't17_search_activity_city4' depends on: ['t16_select_hotel_city4']
2025-07-31 14:40:02,737 - gaiav2.execution.tool_executor - INFO -   Task 't18_select_activity_city4' depends on: ['t17_search_activity_city4']
2025-07-31 14:40:02,738 - gaiav2.execution.tool_executor - INFO -   Task 't19_search_hotels_city5' depends on: ['t2_rank_cities']
2025-07-31 14:40:02,738 - gaiav2.execution.tool_executor - INFO -   Task 't20_select_hotel_city5' depends on: ['t19_search_hotels_city5']
2025-07-31 14:40:02,738 - gaiav2.execution.tool_executor - INFO -   Task 't21_search_activity_city5' depends on: ['t20_select_hotel_city5']
2025-07-31 14:40:02,738 - gaiav2.execution.tool_executor - INFO -   Task 't22_select_activity_city5' depends on: ['t21_search_activity_city5']
2025-07-31 14:40:02,739 - gaiav2.execution.tool_executor - INFO -   Task 't23_generate_final_answers' depends on: ['t6_select_activity_city1', 't10_select_activity_city2', 't14_select_activity_city3', 't18_select_activity_city4', 't22_select_activity_city5', 't2_rank_cities', 't4_select_hotel_city1', 't8_select_hotel_city2', 't12_select_hotel_city3', 't16_select_hotel_city4', 't20_select_hotel_city5']
2025-07-31 14:40:02,739 - gaiav2.execution.tool_executor - INFO - Dependency graph validated: No cycles found.
2025-07-31 14:40:02,739 - root - INFO - Step 6.1: Registering task templates for dynamic task generation
2025-07-31 14:40:02,740 - gaiav2.execution.tool_executor - INFO - Registering task template: process_ranked_items
2025-07-31 14:40:02,740 - gaiav2.execution.tool_executor - INFO - Registering task template: process_selected_options
2025-07-31 14:40:02,740 - gaiav2.execution.tool_executor - INFO - Registering task template: conditional_high_score
2025-07-31 14:40:02,741 - root - INFO - Step 6.1 complete: Task templates registered
2025-07-31 14:42:08,141 - gaiav2.execution.tool_executor - INFO - Starting plan execution...
2025-07-31 14:42:08,146 - gaiav2.execution.tool_executor - INFO - Adding task 't1_search_cities' to current execution batch.
2025-07-31 14:42:08,147 - gaiav2.execution.tool_executor - INFO - Executing batch of 1 tasks: ['t1_search_cities']
2025-07-31 14:42:08,150 - gaiav2.execution.tool_executor - INFO - Executing task 't1_search_cities': Global search for best cities to visit in Thailand (Action: global_search)
2025-07-31 14:42:08,153 - gaiav2.execution.tool_executor - WARNING - Tool function global_search (search) is synchronous. Running in executor.
2025-07-31 14:43:08,338 - gaiav2.execution.tool_executor - INFO - Task 't1_search_cities' (Global search for best cities to visit in Thailand) raw result: Type <class 'dict'>
2025-07-31 14:43:08,341 - gaiav2.execution.tool_executor - INFO - Task 't1_search_cities' completed successfully.
2025-07-31 14:43:08,356 - gaiav2.execution.tool_executor - INFO - Saved result and input parameters for task 't1_search_cities' to context/tasks/t1_search_cities_result.json
2025-07-31 14:43:08,357 - gaiav2.execution.tool_executor - INFO - Adding task 't2_rank_cities' to current execution batch.
2025-07-31 14:43:08,357 - gaiav2.execution.tool_executor - INFO - Executing batch of 1 tasks: ['t2_rank_cities']
2025-07-31 14:43:08,358 - gaiav2.execution.tool_executor - INFO - Executing task 't2_rank_cities': Rank the top 5 Thai cities for the trip (Action: rank_data)
2025-07-31 14:43:08,400 - gaiav2.execution.tool_executor - ERROR - Error during execution of task 't2_rank_cities' (Action: rank_data): 'RankDataTool' object has no attribute 'execute_tool'
Traceback (most recent call last):
  File "/Users/<USER>/code/snipesearch/gaiav2/execution/tool_executor.py", line 341, in execute_task
    result = await self._execute_tool_function(action, processed_params)
  File "/Users/<USER>/code/snipesearch/gaiav2/execution/tool_executor.py", line 936, in _execute_tool_function
    return await tool_func(**processed_parameters)
  File "/Users/<USER>/code/snipesearch/gaiav2/tools/rank_data/rank_data.py", line 69, in rank_data
    return await _default_tool.rank_data(prompt, input_data, criteria)
  File "/Users/<USER>/code/snipesearch/gaiav2/tools/rank_data/rank_data_tool.py", line 84, in rank_data
    return await self.process(prompt, input_data, criteria)
  File "/Users/<USER>/code/snipesearch/gaiav2/tools/rank_data/rank_data_tool.py", line 63, in process
    return await self.execute_tool(
AttributeError: 'RankDataTool' object has no attribute 'execute_tool'
2025-07-31 14:43:09,598 - gaiav2.execution.tool_executor - ERROR - Task 't2_rank_cities' execution failed: 'RankDataTool' object has no attribute 'execute_tool'
NoneType: None
2025-07-31 14:43:09,598 - gaiav2.execution.tool_executor - ERROR - Deadlock: No tasks ready, none running, but 21 tasks pending and not directly blocked by failed dependencies.
2025-07-31 14:43:09,598 - gaiav2.execution.tool_executor - ERROR -   Task t22_select_activity_city5 (deadlocked) unmet non-failed dependencies: ['t21_search_activity_city5']
2025-07-31 14:43:09,598 - gaiav2.execution.tool_executor - ERROR -   Task t15_search_hotels_city4 (deadlocked) unmet non-failed dependencies: []
2025-07-31 14:43:09,598 - gaiav2.execution.tool_executor - ERROR -   Task t23_generate_final_answers (deadlocked) unmet non-failed dependencies: ['t6_select_activity_city1', 't10_select_activity_city2', 't14_select_activity_city3', 't18_select_activity_city4', 't4_select_hotel_city1', 't8_select_hotel_city2', 't12_select_hotel_city3', 't16_select_hotel_city4', 't20_select_hotel_city5']
2025-07-31 14:43:09,599 - gaiav2.execution.tool_executor - ERROR -   Task t20_select_hotel_city5 (deadlocked) unmet non-failed dependencies: ['t19_search_hotels_city5']
2025-07-31 14:43:09,599 - gaiav2.execution.tool_executor - ERROR -   Task t5_search_activity_city1 (deadlocked) unmet non-failed dependencies: ['t4_select_hotel_city1']
2025-07-31 14:43:09,599 - gaiav2.execution.tool_executor - ERROR -   Task t18_select_activity_city4 (deadlocked) unmet non-failed dependencies: ['t17_search_activity_city4']
2025-07-31 14:43:09,599 - gaiav2.execution.tool_executor - ERROR -   Task t14_select_activity_city3 (deadlocked) unmet non-failed dependencies: ['t13_search_activity_city3']
2025-07-31 14:43:09,599 - gaiav2.execution.tool_executor - ERROR -   Task t7_search_hotels_city2 (deadlocked) unmet non-failed dependencies: []
2025-07-31 14:43:09,599 - gaiav2.execution.tool_executor - ERROR -   Task t4_select_hotel_city1 (deadlocked) unmet non-failed dependencies: ['t3_search_hotels_city1']
2025-07-31 14:43:09,599 - gaiav2.execution.tool_executor - ERROR -   Task t16_select_hotel_city4 (deadlocked) unmet non-failed dependencies: []
2025-07-31 14:43:09,599 - gaiav2.execution.tool_executor - ERROR -   Task t12_select_hotel_city3 (deadlocked) unmet non-failed dependencies: ['t11_search_hotels_city3']
2025-07-31 14:43:09,599 - gaiav2.execution.tool_executor - ERROR -   Task t3_search_hotels_city1 (deadlocked) unmet non-failed dependencies: []
2025-07-31 14:43:09,600 - gaiav2.execution.tool_executor - ERROR -   Task t21_search_activity_city5 (deadlocked) unmet non-failed dependencies: []
2025-07-31 14:43:09,601 - gaiav2.execution.tool_executor - ERROR -   Task t6_select_activity_city1 (deadlocked) unmet non-failed dependencies: []
2025-07-31 14:43:09,601 - gaiav2.execution.tool_executor - ERROR -   Task t11_search_hotels_city3 (deadlocked) unmet non-failed dependencies: []
2025-07-31 14:43:09,601 - gaiav2.execution.tool_executor - ERROR -   Task t17_search_activity_city4 (deadlocked) unmet non-failed dependencies: []
2025-07-31 14:43:09,601 - gaiav2.execution.tool_executor - ERROR -   Task t19_search_hotels_city5 (deadlocked) unmet non-failed dependencies: []
2025-07-31 14:43:09,601 - gaiav2.execution.tool_executor - ERROR -   Task t8_select_hotel_city2 (deadlocked) unmet non-failed dependencies: []
2025-07-31 14:43:09,601 - gaiav2.execution.tool_executor - ERROR -   Task t13_search_activity_city3 (deadlocked) unmet non-failed dependencies: []
2025-07-31 14:43:09,601 - gaiav2.execution.tool_executor - ERROR -   Task t10_select_activity_city2 (deadlocked) unmet non-failed dependencies: ['t9_search_activity_city2']
2025-07-31 14:43:09,601 - gaiav2.execution.tool_executor - ERROR -   Task t9_search_activity_city2 (deadlocked) unmet non-failed dependencies: []
2025-07-31 14:43:09,601 - gaiav2.execution.tool_executor - INFO - All 23 tasks accounted for. Completed: 1, Failed: 22.
2025-07-31 14:43:09,608 - gaiav2.execution.tool_executor - INFO - Execution summary saved to context/execution_summary_20250731_144309.json
2025-07-31 14:43:09,609 - root - INFO - Step 6 complete: All tasks executed and result files generated.
