2025-07-31 15:01:07,145 - root - INFO - Attempting to load plan from: ../test_plan/plan_20250511_193326.json
2025-07-31 15:01:07,145 - root - INFO - Successfully loaded plan from ../test_plan/plan_20250511_193326.json
2025-07-31 15:01:07,146 - root - INFO - Step 2: Initializing WebPlanner and constructing graph
2025-07-31 15:01:07,146 - root - INFO - Step 2 complete: Graph constructed
2025-07-31 15:01:07,146 - root - INFO - Step 3: Plotting the plan graph
2025-07-31 15:01:07,147 - root - INFO - Using multipartite layout based on node depth, stretched horizontally.
2025-07-31 15:01:07,809 - root - INFO - Step 3 complete: Plan graph plotted
2025-07-31 15:01:07,809 - root - INFO - Step 4: Generating primitive execution schedule
2025-07-31 15:01:07,809 - root - INFO - Skipping legacy PrimitiveExecutor schedule/action_map generation as ToolExecutor will handle it.
2025-07-31 15:01:07,810 - root - INFO - Step 6: Executing all levels of tasks using the tools library
2025-07-31 15:01:07,810 - gaiav2.execution.tool_executor - INFO - Dependency graph built:
2025-07-31 15:01:07,810 - gaiav2.execution.tool_executor - INFO -   Task 't1_search_cities' has no dependencies.
2025-07-31 15:01:07,810 - gaiav2.execution.tool_executor - INFO -   Task 't2_rank_cities' depends on: ['t1_search_cities']
2025-07-31 15:01:07,810 - gaiav2.execution.tool_executor - INFO -   Task 't3_search_hotels_city1' depends on: ['t2_rank_cities']
2025-07-31 15:01:07,810 - gaiav2.execution.tool_executor - INFO -   Task 't4_select_hotel_city1' depends on: ['t3_search_hotels_city1']
2025-07-31 15:01:07,810 - gaiav2.execution.tool_executor - INFO -   Task 't5_search_activity_city1' depends on: ['t4_select_hotel_city1']
2025-07-31 15:01:07,810 - gaiav2.execution.tool_executor - INFO -   Task 't6_select_activity_city1' depends on: ['t5_search_activity_city1']
2025-07-31 15:01:07,810 - gaiav2.execution.tool_executor - INFO -   Task 't7_search_hotels_city2' depends on: ['t2_rank_cities']
2025-07-31 15:01:07,810 - gaiav2.execution.tool_executor - INFO -   Task 't8_select_hotel_city2' depends on: ['t7_search_hotels_city2']
2025-07-31 15:01:07,810 - gaiav2.execution.tool_executor - INFO -   Task 't9_search_activity_city2' depends on: ['t8_select_hotel_city2']
2025-07-31 15:01:07,810 - gaiav2.execution.tool_executor - INFO -   Task 't10_select_activity_city2' depends on: ['t9_search_activity_city2']
2025-07-31 15:01:07,810 - gaiav2.execution.tool_executor - INFO -   Task 't11_search_hotels_city3' depends on: ['t2_rank_cities']
2025-07-31 15:01:07,810 - gaiav2.execution.tool_executor - INFO -   Task 't12_select_hotel_city3' depends on: ['t11_search_hotels_city3']
2025-07-31 15:01:07,810 - gaiav2.execution.tool_executor - INFO -   Task 't13_search_activity_city3' depends on: ['t12_select_hotel_city3']
2025-07-31 15:01:07,810 - gaiav2.execution.tool_executor - INFO -   Task 't14_select_activity_city3' depends on: ['t13_search_activity_city3']
2025-07-31 15:01:07,810 - gaiav2.execution.tool_executor - INFO -   Task 't15_search_hotels_city4' depends on: ['t2_rank_cities']
2025-07-31 15:01:07,811 - gaiav2.execution.tool_executor - INFO -   Task 't16_select_hotel_city4' depends on: ['t15_search_hotels_city4']
2025-07-31 15:01:07,811 - gaiav2.execution.tool_executor - INFO -   Task 't17_search_activity_city4' depends on: ['t16_select_hotel_city4']
2025-07-31 15:01:07,811 - gaiav2.execution.tool_executor - INFO -   Task 't18_select_activity_city4' depends on: ['t17_search_activity_city4']
2025-07-31 15:01:07,811 - gaiav2.execution.tool_executor - INFO -   Task 't19_search_hotels_city5' depends on: ['t2_rank_cities']
2025-07-31 15:01:07,811 - gaiav2.execution.tool_executor - INFO -   Task 't20_select_hotel_city5' depends on: ['t19_search_hotels_city5']
2025-07-31 15:01:07,811 - gaiav2.execution.tool_executor - INFO -   Task 't21_search_activity_city5' depends on: ['t20_select_hotel_city5']
2025-07-31 15:01:07,811 - gaiav2.execution.tool_executor - INFO -   Task 't22_select_activity_city5' depends on: ['t21_search_activity_city5']
2025-07-31 15:01:07,811 - gaiav2.execution.tool_executor - INFO -   Task 't23_generate_final_answers' depends on: ['t6_select_activity_city1', 't10_select_activity_city2', 't14_select_activity_city3', 't18_select_activity_city4', 't22_select_activity_city5', 't2_rank_cities', 't4_select_hotel_city1', 't8_select_hotel_city2', 't12_select_hotel_city3', 't16_select_hotel_city4', 't20_select_hotel_city5']
2025-07-31 15:01:07,811 - gaiav2.execution.tool_executor - INFO - Dependency graph validated: No cycles found.
2025-07-31 15:01:07,811 - root - INFO - Step 6.1: Registering task templates for dynamic task generation
2025-07-31 15:01:07,811 - gaiav2.execution.tool_executor - INFO - Registering task template: process_ranked_items
2025-07-31 15:01:07,811 - gaiav2.execution.tool_executor - INFO - Registering task template: process_selected_options
2025-07-31 15:01:07,811 - gaiav2.execution.tool_executor - INFO - Registering task template: conditional_high_score
2025-07-31 15:01:07,811 - root - INFO - Step 6.1 complete: Task templates registered
2025-07-31 15:01:07,811 - gaiav2.execution.tool_executor - INFO - Starting plan execution...
2025-07-31 15:01:07,811 - gaiav2.execution.tool_executor - INFO - Adding task 't1_search_cities' to current execution batch.
2025-07-31 15:01:07,811 - gaiav2.execution.tool_executor - INFO - Executing batch of 1 tasks: ['t1_search_cities']
2025-07-31 15:01:07,812 - gaiav2.execution.tool_executor - INFO - Executing task 't1_search_cities': Global search for best cities to visit in Thailand (Action: global_search)
2025-07-31 15:01:07,812 - gaiav2.execution.tool_executor - WARNING - Tool function global_search (search) is synchronous. Running in executor.
2025-07-31 15:01:10,843 - gaiav2.execution.tool_executor - ERROR - Error during execution of task 't1_search_cities' (Action: global_search): All 3 attempts failed. Last error: Request failed: HTTPConnectionPool(host='localhost', port=9281): Max retries exceeded with url: /search (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x10b03f610>: Failed to establish a new connection: [Errno 61] Connection refused'))
Traceback (most recent call last):
  File "/Users/<USER>/code/snipesearch/gaiav2/execution/tool_executor.py", line 341, in execute_task
    result = await self._execute_tool_function(action, processed_params)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/code/snipesearch/gaiav2/execution/tool_executor.py", line 941, in _execute_tool_function
    return await loop.run_in_executor(None, partial_func)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.13/3.13.5/Frameworks/Python.framework/Versions/3.13/lib/python3.13/concurrent/futures/thread.py", line 59, in run
    result = self.fn(*self.args, **self.kwargs)
  File "/Users/<USER>/code/snipesearch/gaiav2/tools/serp/serp.py", line 42, in search
    return tool.search(query=query, engines=engines, max_results=max_results, **kwargs)
           ~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/code/snipesearch/gaiav2/tools/serp/serp_tool.py", line 129, in search
    raise Exception(f"All {retries + 1} attempts failed. Last error: {last_error}")
Exception: All 3 attempts failed. Last error: Request failed: HTTPConnectionPool(host='localhost', port=9281): Max retries exceeded with url: /search (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x10b03f610>: Failed to establish a new connection: [Errno 61] Connection refused'))
2025-07-31 15:01:10,848 - gaiav2.execution.tool_executor - ERROR - Task 't1_search_cities' execution failed: All 3 attempts failed. Last error: Request failed: HTTPConnectionPool(host='localhost', port=9281): Max retries exceeded with url: /search (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x10b03f610>: Failed to establish a new connection: [Errno 61] Connection refused'))
NoneType: None
2025-07-31 15:01:10,849 - gaiav2.execution.tool_executor - ERROR - Deadlock: No tasks ready, none running, but 22 tasks pending and not directly blocked by failed dependencies.
2025-07-31 15:01:10,849 - gaiav2.execution.tool_executor - ERROR -   Task t19_search_hotels_city5 (deadlocked) unmet non-failed dependencies: ['t2_rank_cities']
2025-07-31 15:01:10,849 - gaiav2.execution.tool_executor - ERROR -   Task t12_select_hotel_city3 (deadlocked) unmet non-failed dependencies: ['t11_search_hotels_city3']
2025-07-31 15:01:10,849 - gaiav2.execution.tool_executor - ERROR -   Task t7_search_hotels_city2 (deadlocked) unmet non-failed dependencies: ['t2_rank_cities']
2025-07-31 15:01:10,849 - gaiav2.execution.tool_executor - ERROR -   Task t18_select_activity_city4 (deadlocked) unmet non-failed dependencies: ['t17_search_activity_city4']
2025-07-31 15:01:10,849 - gaiav2.execution.tool_executor - ERROR -   Task t4_select_hotel_city1 (deadlocked) unmet non-failed dependencies: ['t3_search_hotels_city1']
2025-07-31 15:01:10,849 - gaiav2.execution.tool_executor - ERROR -   Task t5_search_activity_city1 (deadlocked) unmet non-failed dependencies: []
2025-07-31 15:01:10,849 - gaiav2.execution.tool_executor - ERROR -   Task t17_search_activity_city4 (deadlocked) unmet non-failed dependencies: ['t16_select_hotel_city4']
2025-07-31 15:01:10,849 - gaiav2.execution.tool_executor - ERROR -   Task t20_select_hotel_city5 (deadlocked) unmet non-failed dependencies: []
2025-07-31 15:01:10,849 - gaiav2.execution.tool_executor - ERROR -   Task t8_select_hotel_city2 (deadlocked) unmet non-failed dependencies: []
2025-07-31 15:01:10,849 - gaiav2.execution.tool_executor - ERROR -   Task t13_search_activity_city3 (deadlocked) unmet non-failed dependencies: []
2025-07-31 15:01:10,849 - gaiav2.execution.tool_executor - ERROR -   Task t3_search_hotels_city1 (deadlocked) unmet non-failed dependencies: ['t2_rank_cities']
2025-07-31 15:01:10,849 - gaiav2.execution.tool_executor - ERROR -   Task t15_search_hotels_city4 (deadlocked) unmet non-failed dependencies: ['t2_rank_cities']
2025-07-31 15:01:10,849 - gaiav2.execution.tool_executor - ERROR -   Task t9_search_activity_city2 (deadlocked) unmet non-failed dependencies: []
2025-07-31 15:01:10,849 - gaiav2.execution.tool_executor - ERROR -   Task t16_select_hotel_city4 (deadlocked) unmet non-failed dependencies: []
2025-07-31 15:01:10,849 - gaiav2.execution.tool_executor - ERROR -   Task t21_search_activity_city5 (deadlocked) unmet non-failed dependencies: []
2025-07-31 15:01:10,849 - gaiav2.execution.tool_executor - ERROR -   Task t23_generate_final_answers (deadlocked) unmet non-failed dependencies: ['t6_select_activity_city1', 't10_select_activity_city2', 't14_select_activity_city3', 't22_select_activity_city5', 't2_rank_cities']
2025-07-31 15:01:10,849 - gaiav2.execution.tool_executor - ERROR -   Task t11_search_hotels_city3 (deadlocked) unmet non-failed dependencies: ['t2_rank_cities']
2025-07-31 15:01:10,849 - gaiav2.execution.tool_executor - ERROR -   Task t14_select_activity_city3 (deadlocked) unmet non-failed dependencies: []
2025-07-31 15:01:10,849 - gaiav2.execution.tool_executor - ERROR -   Task t6_select_activity_city1 (deadlocked) unmet non-failed dependencies: []
2025-07-31 15:01:10,849 - gaiav2.execution.tool_executor - ERROR -   Task t10_select_activity_city2 (deadlocked) unmet non-failed dependencies: []
2025-07-31 15:01:10,849 - gaiav2.execution.tool_executor - ERROR -   Task t22_select_activity_city5 (deadlocked) unmet non-failed dependencies: []
2025-07-31 15:01:10,849 - gaiav2.execution.tool_executor - ERROR -   Task t2_rank_cities (deadlocked) unmet non-failed dependencies: []
2025-07-31 15:01:10,849 - gaiav2.execution.tool_executor - INFO - All 23 tasks accounted for. Completed: 0, Failed: 23.
2025-07-31 15:01:10,850 - gaiav2.execution.tool_executor - INFO - Execution summary saved to context/execution_summary_20250731_150110.json
2025-07-31 15:01:10,850 - root - INFO - Step 6 complete: All tasks executed and result files generated.
