2025-07-31 15:42:41,241 - root - INFO - OpenRouter provider initialized
2025-07-31 15:42:41,242 - root - INFO - Step 1 (Iteration 1/5): Decomposing query...
2025-07-31 15:42:44,461 - httpx - INFO - HTTP Request: POST https://openrouter.ai/api/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-31 15:42:45,091 - root - INFO - DECOMPOSE | Sending planner prompt to LLM...
2025-07-31 15:42:45,091 - root - INFO - Setting up parameters for thinking model: o3
2025-07-31 15:42:45,091 - root - INFO - Setting reasoning_effort=high for thinking model: o3
2025-07-31 15:42:45,091 - root - INFO - Using thinking model: o3
2025-07-31 15:42:45,091 - root - INFO - Setting reasoning_effort=high for model o3
2025-07-31 15:44:59,947 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-31 15:44:59,951 - root - INFO - DECOMPOSE | Raw response content received (first 500 chars): {
  "tasks": [
    {
      "id": "t1_search_walmart_nearby",
      "type": "primitive",
      "name": "Find nearby Walmart stores within 5 miles",
      "orchestration_action": "Google Maps",
      "parameters": {
        "query": "Walmart near 5th Ave, Austin, TX",
        "radius": "5 miles"
      },
      "preconditions": []
    },
    {
      "id": "t2_search_target_nearby",
      "type": "primitive",
      "name": "Find nearby Target stores within 5 miles",
      "orchestration_action": "Go...
2025-07-31 15:44:59,951 - root - INFO - DECOMPOSE | Successfully parsed raw response as JSON.
2025-07-31 15:44:59,951 - root - INFO - DECOMPOSE | Detected HTN-like plan structure with 'tasks' key.
2025-07-31 15:44:59,951 - root - INFO - DECOMPOSE | Successfully processed into primitives-only plan with 20 task(s).
2025-07-31 15:44:59,951 - root - INFO - Step 1 complete: Decomposition successful.
2025-07-31 15:44:59,953 - root - INFO - Plan saved to context/plan_20250731_154459.json
2025-07-31 15:44:59,954 - root - INFO - Step 2: Initializing WebPlanner and constructing graph
2025-07-31 15:44:59,954 - root - INFO - Step 2 complete: Graph constructed
2025-07-31 15:44:59,954 - root - INFO - Step 3: Plotting the plan graph
2025-07-31 15:44:59,956 - root - INFO - Using multipartite layout based on node depth, stretched horizontally.
2025-07-31 15:45:00,358 - root - INFO - Step 3 complete: Plan graph plotted
2025-07-31 15:45:00,359 - root - INFO - Step 4: Generating primitive execution schedule
2025-07-31 15:45:00,359 - root - INFO - Skipping legacy PrimitiveExecutor schedule/action_map generation as ToolExecutor will handle it.
2025-07-31 15:45:00,359 - root - INFO - Step 6: Executing all levels of tasks using the tools library
2025-07-31 15:45:00,360 - gaiav2.execution.tool_executor - INFO - Dependency graph built:
2025-07-31 15:45:00,360 - gaiav2.execution.tool_executor - INFO -   Task 't1_search_walmart_nearby' has no dependencies.
2025-07-31 15:45:00,360 - gaiav2.execution.tool_executor - INFO -   Task 't2_search_target_nearby' has no dependencies.
2025-07-31 15:45:00,360 - gaiav2.execution.tool_executor - INFO -   Task 't3_search_costco_nearby' has no dependencies.
2025-07-31 15:45:00,360 - gaiav2.execution.tool_executor - INFO -   Task 't4_walmart_search_shrimp' depends on: ['t1_search_walmart_nearby']
2025-07-31 15:45:00,360 - gaiav2.execution.tool_executor - INFO -   Task 't5_walmart_search_steak' depends on: ['t1_search_walmart_nearby']
2025-07-31 15:45:00,360 - gaiav2.execution.tool_executor - INFO -   Task 't6_walmart_search_butter' depends on: ['t1_search_walmart_nearby']
2025-07-31 15:45:00,360 - gaiav2.execution.tool_executor - INFO -   Task 't7_walmart_search_tomato' depends on: ['t1_search_walmart_nearby']
2025-07-31 15:45:00,360 - gaiav2.execution.tool_executor - INFO -   Task 't8_walmart_search_potato' depends on: ['t1_search_walmart_nearby']
2025-07-31 15:45:00,360 - gaiav2.execution.tool_executor - INFO -   Task 't9_target_search_shrimp' depends on: ['t2_search_target_nearby']
2025-07-31 15:45:00,360 - gaiav2.execution.tool_executor - INFO -   Task 't10_target_search_steak' depends on: ['t2_search_target_nearby']
2025-07-31 15:45:00,360 - gaiav2.execution.tool_executor - INFO -   Task 't11_target_search_butter' depends on: ['t2_search_target_nearby']
2025-07-31 15:45:00,360 - gaiav2.execution.tool_executor - INFO -   Task 't12_target_search_tomato' depends on: ['t2_search_target_nearby']
2025-07-31 15:45:00,360 - gaiav2.execution.tool_executor - INFO -   Task 't13_target_search_potato' depends on: ['t2_search_target_nearby']
2025-07-31 15:45:00,360 - gaiav2.execution.tool_executor - INFO -   Task 't14_costco_search_shrimp' depends on: ['t3_search_costco_nearby']
2025-07-31 15:45:00,360 - gaiav2.execution.tool_executor - INFO -   Task 't15_costco_search_steak' depends on: ['t3_search_costco_nearby']
2025-07-31 15:45:00,360 - gaiav2.execution.tool_executor - INFO -   Task 't16_costco_search_butter' depends on: ['t3_search_costco_nearby']
2025-07-31 15:45:00,360 - gaiav2.execution.tool_executor - INFO -   Task 't17_costco_search_tomato' depends on: ['t3_search_costco_nearby']
2025-07-31 15:45:00,360 - gaiav2.execution.tool_executor - INFO -   Task 't18_costco_search_potato' depends on: ['t3_search_costco_nearby']
2025-07-31 15:45:00,360 - gaiav2.execution.tool_executor - INFO -   Task 't19_evaluate_store_options' depends on: ['t1_search_walmart_nearby', 't2_search_target_nearby', 't3_search_costco_nearby', 't4_walmart_search_shrimp', 't5_walmart_search_steak', 't6_walmart_search_butter', 't7_walmart_search_tomato', 't8_walmart_search_potato', 't9_target_search_shrimp', 't10_target_search_steak', 't11_target_search_butter', 't12_target_search_tomato', 't13_target_search_potato', 't14_costco_search_shrimp', 't15_costco_search_steak', 't16_costco_search_butter', 't17_costco_search_tomato', 't18_costco_search_potato']
2025-07-31 15:45:00,360 - gaiav2.execution.tool_executor - INFO -   Task 't20_generate_final_answer' depends on: ['t19_evaluate_store_options']
2025-07-31 15:45:00,360 - gaiav2.execution.tool_executor - INFO - Dependency graph validated: No cycles found.
2025-07-31 15:45:00,361 - root - INFO - Step 6.1: Registering task templates for dynamic task generation
2025-07-31 15:45:00,362 - gaiav2.execution.tool_executor - INFO - Registering task template: process_ranked_items
2025-07-31 15:45:00,362 - gaiav2.execution.tool_executor - INFO - Registering task template: process_selected_options
2025-07-31 15:45:00,362 - gaiav2.execution.tool_executor - INFO - Registering task template: conditional_high_score
2025-07-31 15:45:00,362 - root - INFO - Step 6.1 complete: Task templates registered
2025-07-31 15:45:23,548 - gaiav2.execution.tool_executor - INFO - Starting plan execution...
2025-07-31 15:45:23,550 - gaiav2.execution.tool_executor - INFO - Adding task 't3_search_costco_nearby' to current execution batch.
2025-07-31 15:45:23,550 - gaiav2.execution.tool_executor - INFO - Adding task 't2_search_target_nearby' to current execution batch.
2025-07-31 15:45:23,550 - gaiav2.execution.tool_executor - INFO - Adding task 't1_search_walmart_nearby' to current execution batch.
2025-07-31 15:45:23,550 - gaiav2.execution.tool_executor - INFO - Executing batch of 3 tasks: ['t3_search_costco_nearby', 't2_search_target_nearby', 't1_search_walmart_nearby']
2025-07-31 15:45:23,551 - gaiav2.execution.tool_executor - INFO - Executing task 't3_search_costco_nearby': Find nearby Costco stores within 5 miles (Action: Google Maps)
2025-07-31 15:45:23,553 - gaiav2.execution.tool_executor - WARNING - Tool function Google Maps (search_places) is synchronous. Running in executor.
2025-07-31 15:45:23,554 - gaiav2.execution.tool_executor - INFO - Executing task 't2_search_target_nearby': Find nearby Target stores within 5 miles (Action: Google Maps)
2025-07-31 15:45:23,555 - gaiav2.execution.tool_executor - WARNING - Tool function Google Maps (search_places) is synchronous. Running in executor.
2025-07-31 15:45:23,556 - gaiav2.execution.tool_executor - INFO - Executing task 't1_search_walmart_nearby': Find nearby Walmart stores within 5 miles (Action: Google Maps)
2025-07-31 15:45:23,556 - gaiav2.execution.tool_executor - WARNING - Tool function Google Maps (search_places) is synchronous. Running in executor.
2025-07-31 15:45:23,581 - gaiav2.execution.tool_executor - ERROR - Error during execution of task 't2_search_target_nearby' (Action: Google Maps): Request failed: 422 Client Error: Unprocessable Entity for url: http://localhost:9284/places/search?query=Target+near+5th+Ave%2C+Austin%2C+TX&radius=5+miles&debug=False&language=en&api_key=AIzaSyCW4_4YOm9FUtCQU6FtMuMbhQFBOT8xafg. Response: {"detail": [{"type": "int_parsing", "loc": ["query", "radius"], "msg": "Input should be a valid integer, unable to parse string as an integer", "input": "5 miles"}]}
Traceback (most recent call last):
  File "/Users/<USER>/code/snipesearch/gaiav2/tools/base_tool.py", line 116, in _make_request
    response.raise_for_status()
  File "/Users/<USER>/code/snipesearch/.venv/lib/python3.9/site-packages/requests/models.py", line 1024, in raise_for_status
    raise HTTPError(http_error_msg, response=self)
requests.exceptions.HTTPError: 422 Client Error: Unprocessable Entity for url: http://localhost:9284/places/search?query=Target+near+5th+Ave%2C+Austin%2C+TX&radius=5+miles&debug=False&language=en&api_key=AIzaSyCW4_4YOm9FUtCQU6FtMuMbhQFBOT8xafg

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/code/snipesearch/gaiav2/execution/tool_executor.py", line 341, in execute_task
    result = await self._execute_tool_function(action, processed_params)
  File "/Users/<USER>/code/snipesearch/gaiav2/execution/tool_executor.py", line 941, in _execute_tool_function
    return await loop.run_in_executor(None, partial_func)
  File "/Applications/Xcode.app/Contents/Developer/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/concurrent/futures/thread.py", line 52, in run
    result = self.fn(*self.args, **self.kwargs)
  File "/Users/<USER>/code/snipesearch/gaiav2/tools/maps/maps.py", line 59, in search_places
    return _default_tool.search_places(
  File "/Users/<USER>/code/snipesearch/gaiav2/tools/maps/maps_tool.py", line 115, in search_places
    return self._make_request("GET", "/places/search", params=params)
  File "/Users/<USER>/code/snipesearch/gaiav2/tools/base_tool.py", line 127, in _make_request
    raise Exception(error_msg)
Exception: Request failed: 422 Client Error: Unprocessable Entity for url: http://localhost:9284/places/search?query=Target+near+5th+Ave%2C+Austin%2C+TX&radius=5+miles&debug=False&language=en&api_key=AIzaSyCW4_4YOm9FUtCQU6FtMuMbhQFBOT8xafg. Response: {"detail": [{"type": "int_parsing", "loc": ["query", "radius"], "msg": "Input should be a valid integer, unable to parse string as an integer", "input": "5 miles"}]}
2025-07-31 15:45:23,611 - gaiav2.execution.tool_executor - ERROR - Error during execution of task 't1_search_walmart_nearby' (Action: Google Maps): Request failed: 422 Client Error: Unprocessable Entity for url: http://localhost:9284/places/search?query=Walmart+near+5th+Ave%2C+Austin%2C+TX&radius=5+miles&debug=False&language=en&api_key=AIzaSyCW4_4YOm9FUtCQU6FtMuMbhQFBOT8xafg. Response: {"detail": [{"type": "int_parsing", "loc": ["query", "radius"], "msg": "Input should be a valid integer, unable to parse string as an integer", "input": "5 miles"}]}
Traceback (most recent call last):
  File "/Users/<USER>/code/snipesearch/gaiav2/tools/base_tool.py", line 116, in _make_request
    response.raise_for_status()
  File "/Users/<USER>/code/snipesearch/.venv/lib/python3.9/site-packages/requests/models.py", line 1024, in raise_for_status
    raise HTTPError(http_error_msg, response=self)
requests.exceptions.HTTPError: 422 Client Error: Unprocessable Entity for url: http://localhost:9284/places/search?query=Walmart+near+5th+Ave%2C+Austin%2C+TX&radius=5+miles&debug=False&language=en&api_key=AIzaSyCW4_4YOm9FUtCQU6FtMuMbhQFBOT8xafg

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/code/snipesearch/gaiav2/execution/tool_executor.py", line 341, in execute_task
    result = await self._execute_tool_function(action, processed_params)
  File "/Users/<USER>/code/snipesearch/gaiav2/execution/tool_executor.py", line 941, in _execute_tool_function
    return await loop.run_in_executor(None, partial_func)
  File "/Applications/Xcode.app/Contents/Developer/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/concurrent/futures/thread.py", line 52, in run
    result = self.fn(*self.args, **self.kwargs)
  File "/Users/<USER>/code/snipesearch/gaiav2/tools/maps/maps.py", line 59, in search_places
    return _default_tool.search_places(
  File "/Users/<USER>/code/snipesearch/gaiav2/tools/maps/maps_tool.py", line 115, in search_places
    return self._make_request("GET", "/places/search", params=params)
  File "/Users/<USER>/code/snipesearch/gaiav2/tools/base_tool.py", line 127, in _make_request
    raise Exception(error_msg)
Exception: Request failed: 422 Client Error: Unprocessable Entity for url: http://localhost:9284/places/search?query=Walmart+near+5th+Ave%2C+Austin%2C+TX&radius=5+miles&debug=False&language=en&api_key=AIzaSyCW4_4YOm9FUtCQU6FtMuMbhQFBOT8xafg. Response: {"detail": [{"type": "int_parsing", "loc": ["query", "radius"], "msg": "Input should be a valid integer, unable to parse string as an integer", "input": "5 miles"}]}
2025-07-31 15:45:23,611 - gaiav2.execution.tool_executor - ERROR - Error during execution of task 't3_search_costco_nearby' (Action: Google Maps): Request failed: 422 Client Error: Unprocessable Entity for url: http://localhost:9284/places/search?query=Costco+near+5th+Ave%2C+Austin%2C+TX&radius=5+miles&debug=False&language=en&api_key=AIzaSyCW4_4YOm9FUtCQU6FtMuMbhQFBOT8xafg. Response: {"detail": [{"type": "int_parsing", "loc": ["query", "radius"], "msg": "Input should be a valid integer, unable to parse string as an integer", "input": "5 miles"}]}
Traceback (most recent call last):
  File "/Users/<USER>/code/snipesearch/gaiav2/tools/base_tool.py", line 116, in _make_request
    response.raise_for_status()
  File "/Users/<USER>/code/snipesearch/.venv/lib/python3.9/site-packages/requests/models.py", line 1024, in raise_for_status
    raise HTTPError(http_error_msg, response=self)
requests.exceptions.HTTPError: 422 Client Error: Unprocessable Entity for url: http://localhost:9284/places/search?query=Costco+near+5th+Ave%2C+Austin%2C+TX&radius=5+miles&debug=False&language=en&api_key=AIzaSyCW4_4YOm9FUtCQU6FtMuMbhQFBOT8xafg

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/code/snipesearch/gaiav2/execution/tool_executor.py", line 341, in execute_task
    result = await self._execute_tool_function(action, processed_params)
  File "/Users/<USER>/code/snipesearch/gaiav2/execution/tool_executor.py", line 941, in _execute_tool_function
    return await loop.run_in_executor(None, partial_func)
  File "/Applications/Xcode.app/Contents/Developer/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/concurrent/futures/thread.py", line 52, in run
    result = self.fn(*self.args, **self.kwargs)
  File "/Users/<USER>/code/snipesearch/gaiav2/tools/maps/maps.py", line 59, in search_places
    return _default_tool.search_places(
  File "/Users/<USER>/code/snipesearch/gaiav2/tools/maps/maps_tool.py", line 115, in search_places
    return self._make_request("GET", "/places/search", params=params)
  File "/Users/<USER>/code/snipesearch/gaiav2/tools/base_tool.py", line 127, in _make_request
    raise Exception(error_msg)
Exception: Request failed: 422 Client Error: Unprocessable Entity for url: http://localhost:9284/places/search?query=Costco+near+5th+Ave%2C+Austin%2C+TX&radius=5+miles&debug=False&language=en&api_key=AIzaSyCW4_4YOm9FUtCQU6FtMuMbhQFBOT8xafg. Response: {"detail": [{"type": "int_parsing", "loc": ["query", "radius"], "msg": "Input should be a valid integer, unable to parse string as an integer", "input": "5 miles"}]}
2025-07-31 15:45:23,612 - gaiav2.execution.tool_executor - ERROR - Task 't3_search_costco_nearby' execution failed: Request failed: 422 Client Error: Unprocessable Entity for url: http://localhost:9284/places/search?query=Costco+near+5th+Ave%2C+Austin%2C+TX&radius=5+miles&debug=False&language=en&api_key=AIzaSyCW4_4YOm9FUtCQU6FtMuMbhQFBOT8xafg. Response: {"detail": [{"type": "int_parsing", "loc": ["query", "radius"], "msg": "Input should be a valid integer, unable to parse string as an integer", "input": "5 miles"}]}
NoneType: None
2025-07-31 15:45:23,612 - gaiav2.execution.tool_executor - ERROR - Task 't2_search_target_nearby' execution failed: Request failed: 422 Client Error: Unprocessable Entity for url: http://localhost:9284/places/search?query=Target+near+5th+Ave%2C+Austin%2C+TX&radius=5+miles&debug=False&language=en&api_key=AIzaSyCW4_4YOm9FUtCQU6FtMuMbhQFBOT8xafg. Response: {"detail": [{"type": "int_parsing", "loc": ["query", "radius"], "msg": "Input should be a valid integer, unable to parse string as an integer", "input": "5 miles"}]}
NoneType: None
2025-07-31 15:45:23,612 - gaiav2.execution.tool_executor - ERROR - Task 't1_search_walmart_nearby' execution failed: Request failed: 422 Client Error: Unprocessable Entity for url: http://localhost:9284/places/search?query=Walmart+near+5th+Ave%2C+Austin%2C+TX&radius=5+miles&debug=False&language=en&api_key=AIzaSyCW4_4YOm9FUtCQU6FtMuMbhQFBOT8xafg. Response: {"detail": [{"type": "int_parsing", "loc": ["query", "radius"], "msg": "Input should be a valid integer, unable to parse string as an integer", "input": "5 miles"}]}
NoneType: None
2025-07-31 15:45:23,612 - gaiav2.execution.tool_executor - WARNING - Marking task t16_costco_search_butter as failed (blocked by failed dependency t3_search_costco_nearby).
2025-07-31 15:45:23,612 - gaiav2.execution.tool_executor - WARNING - Marking task t11_target_search_butter as failed (blocked by failed dependency t2_search_target_nearby).
2025-07-31 15:45:23,612 - gaiav2.execution.tool_executor - WARNING - Marking task t9_target_search_shrimp as failed (blocked by failed dependency t2_search_target_nearby).
2025-07-31 15:45:23,612 - gaiav2.execution.tool_executor - WARNING - Marking task t8_walmart_search_potato as failed (blocked by failed dependency t1_search_walmart_nearby).
2025-07-31 15:45:23,612 - gaiav2.execution.tool_executor - WARNING - Marking task t10_target_search_steak as failed (blocked by failed dependency t2_search_target_nearby).
2025-07-31 15:45:23,612 - gaiav2.execution.tool_executor - WARNING - Marking task t4_walmart_search_shrimp as failed (blocked by failed dependency t1_search_walmart_nearby).
2025-07-31 15:45:23,612 - gaiav2.execution.tool_executor - ERROR - Deadlock: No tasks ready, none running, but 17 tasks pending and not directly blocked by failed dependencies.
2025-07-31 15:45:23,612 - gaiav2.execution.tool_executor - ERROR -   Task t20_generate_final_answer (deadlocked) unmet non-failed dependencies: ['t19_evaluate_store_options']
2025-07-31 15:45:23,612 - gaiav2.execution.tool_executor - ERROR -   Task t15_costco_search_steak (deadlocked) unmet non-failed dependencies: []
2025-07-31 15:45:23,612 - gaiav2.execution.tool_executor - ERROR -   Task t13_target_search_potato (deadlocked) unmet non-failed dependencies: []
2025-07-31 15:45:23,612 - gaiav2.execution.tool_executor - ERROR -   Task t17_costco_search_tomato (deadlocked) unmet non-failed dependencies: []
2025-07-31 15:45:23,612 - gaiav2.execution.tool_executor - ERROR -   Task t14_costco_search_shrimp (deadlocked) unmet non-failed dependencies: []
2025-07-31 15:45:23,612 - gaiav2.execution.tool_executor - ERROR -   Task t5_walmart_search_steak (deadlocked) unmet non-failed dependencies: []
2025-07-31 15:45:23,612 - gaiav2.execution.tool_executor - ERROR -   Task t6_walmart_search_butter (deadlocked) unmet non-failed dependencies: []
2025-07-31 15:45:23,612 - gaiav2.execution.tool_executor - ERROR -   Task t19_evaluate_store_options (deadlocked) unmet non-failed dependencies: ['t7_walmart_search_tomato', 't12_target_search_tomato', 't18_costco_search_potato']
2025-07-31 15:45:23,612 - gaiav2.execution.tool_executor - ERROR -   Task t12_target_search_tomato (deadlocked) unmet non-failed dependencies: []
2025-07-31 15:45:23,612 - gaiav2.execution.tool_executor - ERROR -   Task t7_walmart_search_tomato (deadlocked) unmet non-failed dependencies: []
2025-07-31 15:45:23,612 - gaiav2.execution.tool_executor - ERROR -   Task t18_costco_search_potato (deadlocked) unmet non-failed dependencies: []
2025-07-31 15:45:23,613 - gaiav2.execution.tool_executor - INFO - All 20 tasks accounted for. Completed: 0, Failed: 20.
2025-07-31 15:45:23,613 - gaiav2.execution.tool_executor - INFO - Execution summary saved to context/execution_summary_20250731_154523.json
2025-07-31 15:45:23,613 - root - INFO - Step 6 complete: All tasks executed and result files generated.
