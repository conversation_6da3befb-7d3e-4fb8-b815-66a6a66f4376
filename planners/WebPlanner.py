import matplotlib.pyplot as plt
import networkx as nx
import re # Added for parsing preconditions


class WebPlanner:
    def __init__(self):
        self.graph = nx.DiGraph()
        self.node_counter = 0  # Simple way to assign unique integer IDs

    def has_plan(self):
        """Check if the planner has any nodes in its graph."""
        return len(self.graph.nodes) > 0

    def add_plan(self, plan, parent_id=None):
        """
        Recursively add nodes and edges from the hierarchical plan.
        'plan' is the JSON object from QueryDecomposer.
        """
        # If plan is a graph-format plan with nodes and edges, import directly
        if isinstance(plan, dict) and 'nodes' in plan and 'edges' in plan:
            id_map = {}
            # Add nodes: use index as node ID initially for graph structure
            for idx, node in enumerate(plan['nodes']):
                node_id_str = node.get('id') # Get the string ID from the plan
                if not node_id_str:
                    print(f"Warning: Node at index {idx} missing 'id' field. Skipping.")
                    continue
                id_map[node_id_str] = idx # Map the string ID to the graph index ID
                self.graph.add_node(
                    idx, # Use index as the graph node identifier
                    # Store the original string ID and other fields from the 'Orchestrate' format
                    original_id=node_id_str,
                    action=node.get('action'),
                    parameters=node.get('parameters'),
                    input_refs=node.get('input_refs'),
                    output_ref=node.get('output_ref'),
                    description=node.get('description'),
                    status='pending', # Keep existing status logic
                    processing_count=0 # Keep existing count logic
                    # Removed fields not in the new prompt: question, criteria, entity, depth, dependencies
                )
                print(f"Added node {idx} (id: '{node_id_str}')")

            # Add edges: map source/target string IDs using id_map
            for edge in plan['edges']:
                src_id_str = edge.get('source')
                tgt_id_str = edge.get('target')
                # Look up the graph index IDs using the string IDs
                src_idx = id_map.get(src_id_str)
                tgt_idx = id_map.get(tgt_id_str)
                if src_idx is None or tgt_idx is None:
                    print(f"Warning: Could not find nodes for edge source '{src_id_str}' or target '{tgt_id_str}'. Skipping edge.")
                    continue
                self.graph.add_edge(src_idx, tgt_idx) # Use index IDs for adding edge
                print(f"Added edge from node {src_idx} ('{src_id_str}') to node {tgt_idx} ('{tgt_id_str}')")
            # Update node counter
            self.node_counter = len(self.graph.nodes) # Use actual graph size
            return
        # HTN-style plan support: tasks + methods
        # Now expects plan['tasks'] to be a list of PRIMITIVE tasks only.
        # The 'methods' key is no longer expected from QueryDecomposer for this path.
        if isinstance(plan, dict) and 'tasks' in plan:
            id_map = {} # Maps original task string IDs to new integer graph node IDs
            
            # Add each HTN task as a node. These should all be primitive.
            current_tasks_in_plan = plan['tasks']
            if not isinstance(current_tasks_in_plan, list):
                print(f"Warning: WebPlanner received 'tasks' but it is not a list. Plan: {plan}")
                return # Or handle error appropriately

            for t in current_tasks_in_plan:
                if not isinstance(t, dict):
                    print(f"Warning: WebPlanner encountered a non-dictionary item in tasks list: {t}. Skipping.")
                    continue

                if t.get('type') != 'primitive':
                    print(f"Warning: WebPlanner received a non-primitive task '{t.get('id')}' in HTN plan. Skipping.")
                    continue

                tid = t.get('id')
                if not tid:
                    print(f"Warning: WebPlanner received a primitive task with no ID. Skipping: {t}")
                    continue
                
                # Assign a unique integer ID for the graph node
                current_graph_node_id = self.node_counter
                id_map[tid] = current_graph_node_id # Map string ID to graph's integer ID
                self.node_counter += 1

                self.graph.add_node(
                    current_graph_node_id, 
                    original_id=tid,
                    task_type=t.get('type'), # Should be 'primitive'
                    action=t.get('orchestration_action'),
                    parameters=t.get('parameters', {}),
                    input_refs=t.get('preconditions', []), # Preconditions list
                    output_ref=tid, # Typically, a task's output is referenced by its own ID
                    description=t.get('name'),
                    status='pending',
                    processing_count=0
                )
                # print(f"Added primitive task node {current_graph_node_id} (original_id: '{tid}')")

            # Add dependency edges based on preconditions/input_refs
            # Iterate over the tasks that were just processed (current_tasks_in_plan)
            for t in current_tasks_in_plan:
                if not isinstance(t, dict) or t.get('type') != 'primitive' or not t.get('id'): # Basic safety checks
                    continue

                tgt_original_id = t.get('id')
                tgt_idx = id_map.get(tgt_original_id) # Get the graph node ID for the current (target) task

                if tgt_idx is None: # Should not happen if task was added to id_map
                    print(f"Error: Could not find graph ID for target task '{tgt_original_id}' when adding dependencies.")
                    continue

                preconditions = t.get('preconditions', [])
                if not preconditions or not isinstance(preconditions, list):
                    continue

                for precond_str in preconditions:
                    if isinstance(precond_str, str):
                        # Extract source task ID from precondition string (e.g., "t1_do_something completed" or just "t1_do_something")
                        # This regex attempts to capture the task ID, which is assumed to be the first word-like part.
                        precond_task_id_match = re.match(r"^([a-zA-Z0-9_\-]+)", precond_str.strip())
                        if precond_task_id_match:
                            src_original_id = precond_task_id_match.group(1)
                            src_idx = id_map.get(src_original_id) # Get graph node ID for the source task

                            if src_idx is not None and tgt_idx is not None:
                                if src_idx != tgt_idx: # Avoid self-loops based on simple ID check
                                    if not self.graph.has_edge(src_idx, tgt_idx):
                                        self.graph.add_edge(src_idx, tgt_idx, edge_type='dependency')
                                        # print(f"Added dependency edge from node {src_idx} ('{src_original_id}') to node {tgt_idx} ('{tgt_original_id}')")
                                    # else: # Edge already exists
                                        # print(f"Dependency edge from {src_idx} to {tgt_idx} already exists.")
                                else:
                                    print(f"Warning: Skipped self-referential precondition for task '{tgt_original_id}' ('{precond_str}').")
                            elif src_idx is None:
                                print(f"Warning: Could not find source task '{src_original_id}' in id_map for precondition '{precond_str}' of task '{tgt_original_id}'. Edge not added.")
                        else:
                            print(f"Warning: Could not parse source task ID from precondition '{precond_str}' for task '{tgt_original_id}'.")

            # self.node_counter is already updated incrementally.
            return # Successfully processed the primitives-only HTN plan

        # Create a unique ID for the current node (Fallback/legacy path, might need review)
        # This part handles the older recursive plan format, not the new JSON DAG format.
        # Consider if this fallback is still needed or should be refactored/removed.
        node_id = self.node_counter
        self.node_counter += 1 # Increment for the next node

        # Add the node to the graph with its question and initial status
        self.graph.add_node(
            node_id,
            question=plan['question'],
            status='pending', # Initial status
            processing_count=0 # Example attribute
            # Add other relevant attributes like depth if needed
        )
        print(f"Added node {node_id}: '{plan['question']}'")

        # If it's not the root node, add a hierarchical edge from its parent
        if parent_id is not None:
            self.graph.add_edge(parent_id, node_id, edge_type='hierarchical')
            print(f"Added hierarchical edge from node {parent_id} to node {node_id}")

        # Recursively process subquestions
        subquestions = plan.get('subquestions', [])
        if subquestions:
            for subplan in subquestions:
                 if subplan: # Ensure subplan is not null/empty
                    # Call add_plan for each subquestion, passing the current node_id as the parent_id
                    self.add_plan(subplan, parent_id=node_id)

    def get_ready_nodes(self):
        """
        Get nodes that are ready to execute (all dependencies satisfied).
        A node is ready if:
        1. It has 'pending' status
        2. All its dependency predecessors have 'completed' status
        """
        ready = []
        for node in self.graph.nodes:
            # Skip nodes that are not pending
            if self.graph.nodes[node]['status'] != 'pending':
                continue

            # Get all predecessors that are dependency edges
            dependency_preds = []
            for pred in self.graph.predecessors(node):
                edge_data = self.graph.get_edge_data(pred, node)
                edge_type = edge_data.get('edge_type', '')
                # Check if this is a dependency edge or a multi-type edge containing dependency
                if edge_type == 'dependency' or 'dependency' in edge_type:
                    dependency_preds.append(pred)

            # If no dependency predecessors, or all are completed, node is ready
            if not dependency_preds or all(self.graph.nodes[p]['status'] == 'completed' for p in dependency_preds):
                ready.append(node)

        return ready

    def mark_completed(self, node_id):
        self.graph.nodes[node_id]['status'] = 'completed'
        print(f"Marked node {node_id} as completed.")

    def print_graph(self):
        print("\nCurrent Graph:")
        for node_id, node_data in self.graph.nodes(data=True):
            # Use 'original_id' for HTN tasks, or 'question' for older format tasks
            display_name = node_data.get('original_id', node_data.get('question', f'Node {node_id}'))
            status = node_data.get('status', '_unknown_status_')
            action = node_data.get('action', '_no_action_')
            task_type = node_data.get('task_type', '_unknown_type_')
            print(f"Node {node_id} (Name/ID: '{display_name}', Type: {task_type}, Action: {action}, Status: {status})")
        
        print("\nEdges:")
        for src, tgt, data in self.graph.edges(data=True):
            edge_type = data.get('edge_type', 'unknown')
            # Get display names for src and tgt nodes
            src_display = self.graph.nodes[src].get('original_id', self.graph.nodes[src].get('question', f'Node {src}'))
            tgt_display = self.graph.nodes[tgt].get('original_id', self.graph.nodes[tgt].get('question', f'Node {tgt}'))
            print(f"Edge from '{src_display}' ({src}) to '{tgt_display}' ({tgt}) - Type: {edge_type}")

    def get_graph(self):
        """Return the NetworkX graph object directly"""
        return self.graph

    def plot_graph(self):
        """
        Plot the graph using NetworkX and Matplotlib.
        Returns the NetworkX graph object instead of the figure.
        """
        # Return the graph object directly. Visualization will be handled by graph_visualization.py
        return self.graph

    def mark_irrelevant(self, node_id, justification):
        """
        Mark the node as irrelevant and store the justification.
        """
        self.graph.nodes[node_id]['status'] = 'irrelevant'
        self.graph.nodes[node_id]['justification'] = justification
        print(f"Node {node_id} marked as irrelevant due to: {justification}")