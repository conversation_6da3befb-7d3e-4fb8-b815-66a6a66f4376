#!/usr/bin/env python3
"""
Simple script to run GaiaV2 from the root directory with real-time streaming.
"""

import sys
import os
import asyncio
import logging
import json
import time
from typing import Dict, Any, Set, List

# Add the current directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Import the main function directly from main.py
from main import main

# Define the path to the development plan
DEVELOPMENT_PLAN_PATH = "../test_plan/plan_20250511_193326.json"
DEVELOPMENT_PLAN_PATH = "context/plan_20250731_154459.json"

# Configure logging to stream to console in real-time
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)

# Monkey patch the ToolExecutor to add real-time streaming
def patch_tool_executor():
    from execution.tool_executor import ToolExecutor

    # Store the original methods
    original_execute_plan = ToolExecutor.execute_plan
    original_execute_task = ToolExecutor.execute_task
    original_save_task_result = ToolExecutor.save_task_result

    # Override execute_plan to add real-time streaming
    async def execute_plan_with_streaming(self) -> Dict[str, Any]:
        print("\n🚀 Starting plan execution with real-time streaming...")
        print("📊 Tasks will be executed based on their dependencies")
        print("📝 Results will be streamed to the terminal as they're generated\n")

        all_task_ids = set(self.action_map.keys())

        # Print initial task status
        print(f"📋 Total tasks: {len(all_task_ids)}")
        print(f"⏳ Tasks ready to run: {len(self.get_ready_tasks())}")
        print(f"🔄 Tasks running: {len(self.running_tasks)}")
        print(f"✅ Tasks completed: {len(self.completed_tasks)}")
        print(f"❌ Tasks failed: {len(self.failed_tasks)}\n")

        # Call the original method to get the results
        return await original_execute_plan(self)

    # Override execute_task to add real-time streaming
    async def execute_task_with_streaming(self, task_id: str):
        task_info = self.action_map.get(task_id)
        task_name = task_info.get("name", task_id) if task_info else task_id
        action = task_info.get("action") if task_info else "unknown"

        print(f"\n🔄 Executing task: {task_name} (ID: {task_id}, Action: {action})")

        start_time = time.time()
        result = await original_execute_task(self, task_id)
        execution_time = time.time() - start_time

        print(f"✅ Completed task: {task_name} in {execution_time:.2f} seconds")

        # If this is a final answer generation task, stream the answer
        if action == "generate_final_answers" and isinstance(result, tuple) and len(result) == 2:
            result_data, params = result
            if isinstance(result_data, dict) and "answer" in result_data:
                print("\n📝 Final Answer Generated:")
                print("=" * 80)
                print(result_data["answer"])
                print("=" * 80)

                if "summary" in result_data and result_data["summary"]:
                    print("\n📌 Summary:")
                    print(result_data["summary"])

                if "recommendations" in result_data and result_data["recommendations"]:
                    print("\n🔍 Recommendations:")
                    for i, rec in enumerate(result_data["recommendations"], 1):
                        print(f"{i}. {rec}")

        return result

    # Override save_task_result to add real-time streaming
    def save_task_result_with_streaming(self, task_id: str, result: Any, parameters: Dict[str, Any]):
        task_info = self.action_map.get(task_id)
        task_name = task_info.get("name", task_id) if task_info else task_id

        print(f"💾 Saving result for task: {task_name}")

        # Call the original method
        original_save_task_result(self, task_id, result, parameters)

        # Update task status
        all_task_ids = set(self.action_map.keys())
        print(f"\n📊 Task Status Update:")
        print(f"📋 Total tasks: {len(all_task_ids)}")
        print(f"⏳ Tasks ready to run: {len(self.get_ready_tasks())}")
        print(f"🔄 Tasks running: {len(self.running_tasks)}")
        print(f"✅ Tasks completed: {len(self.completed_tasks)}")
        print(f"❌ Tasks failed: {len(self.failed_tasks)}")

    # Apply the patches
    ToolExecutor.execute_plan = execute_plan_with_streaming
    ToolExecutor.execute_task = execute_task_with_streaming
    ToolExecutor.save_task_result = save_task_result_with_streaming

    print("🔧 Enhanced ToolExecutor with real-time streaming capabilities")

if __name__ == "__main__":
    # Apply the monkey patches
    patch_tool_executor()

    # Run the async main function with the development plan path
    print(f"📂 Loading plan from: {DEVELOPMENT_PLAN_PATH}")
    asyncio.run(main(DEVELOPMENT_PLAN_PATH))
    # asyncio.run(main())
