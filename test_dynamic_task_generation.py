#!/usr/bin/env python3
"""
Test script for dynamic task generation.

This script demonstrates the dynamic task generation capabilities
for GaiaV2, including iteration over collections of results and
conditional task generation.
"""

import os
import sys
import json
import logging
import asyncio
from typing import Dict, Any, List, Set

# Add the parent directory to the path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import the necessary modules
from gaiav2.execution.tool_executor import ToolExecutor

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def create_sample_action_map() -> Dict[str, Dict[str, Any]]:
    """Create a sample action map for testing."""
    return {
        "t1": {
            "action": "search_hotel",
            "parameters": {
                "destination": "Paris",
                "check_in": "2025-10-01",
                "check_out": "2025-10-05",
                "adults": 2
            },
            "name": "Search hotels in Paris"
        },
        "t2": {
            "action": "rank_data",
            "parameters": {
                "input_data": "results_of_t1",
                "criteria": "price, rating, and location"
            },
            "name": "Rank hotels by price, rating, and location",
            "generate_tasks": {
                "template_id": "process_ranked_hotels",
                "dependencies": ["t2"]
            }
        }
    }

def create_sample_execution_schedule() -> List[Set[str]]:
    """Create a sample execution schedule for testing."""
    return [
        {"t1"},  # Level 1
        {"t2"}   # Level 2
    ]

def create_task_templates() -> Dict[str, Dict[str, Any]]:
    """Create sample task templates for testing."""
    return {
        "process_ranked_hotels": {
            "type": "for_each",
            "id_prefix": "hotel",
            "collection": "results_of_t2.data.ranked_items",
            "action": "generate_structured_data",
            "parameters": {
                "input_data": "${item.item}",
                "output_type": "json",
                "output_filename": "hotel_${index}"
            },
            "name": "Process hotel ${item.item.name}"
        },
        "summarize_hotel": {
            "type": "simple",
            "id_prefix": "summary",
            "action": "generate_final_answers",
            "parameters": {
                "input_data": "${result}",
                "question": "What are the key features of this hotel?",
                "format": "markdown"
            },
            "name": "Summarize hotel ${result.name}"
        },
        "conditional_booking": {
            "type": "conditional",
            "id_prefix": "booking",
            "condition": "${item.score > 85}",
            "then": {
                "action": "generate_structured_data",
                "parameters": {
                    "input_data": "${item.item}",
                    "output_type": "json",
                    "output_filename": "booking_${item.item.name}"
                },
                "name": "Generate booking for ${item.item.name}"
            },
            "else": {
                "action": "generate_structured_data",
                "parameters": {
                    "input_data": "${item.item}",
                    "output_type": "json",
                    "output_filename": "alternative_${item.item.name}"
                },
                "name": "Generate alternatives to ${item.item.name}"
            }
        }
    }

async def test_dynamic_task_generation():
    """Test the dynamic task generation capabilities."""
    # Create the action map and execution schedule
    action_map = create_sample_action_map()
    execution_schedule = create_sample_execution_schedule()
    
    # Create the tool executor
    tool_executor = ToolExecutor(execution_schedule, action_map, user_query="Find me a hotel in Paris")
    
    # Register task templates
    task_templates = create_task_templates()
    for template_id, template in task_templates.items():
        tool_executor.register_task_template(template_id, template)
    
    # Execute all tasks
    print("Executing all tasks...")
    await tool_executor.execute_all_levels()
    
    # Print the results
    print("\nExecution complete!")
    print(f"Total tasks: {len(tool_executor.task_dependencies)}")
    print(f"Completed tasks: {len(tool_executor.completed_tasks)}")
    print(f"Dynamic tasks: {len(tool_executor.dynamic_tasks)}")
    
    # Print the task IDs
    print("\nTask IDs:")
    for task_id in sorted(tool_executor.task_dependencies.keys()):
        task_info = tool_executor.action_map.get(task_id, {})
        task_name = task_info.get("name", task_id)
        action = task_info.get("action", "unknown")
        is_dynamic = task_id in tool_executor.dynamic_tasks
        status = "Completed" if task_id in tool_executor.completed_tasks else "Not executed"
        print(f"- {task_id}: {action} - {task_name} (Dynamic: {is_dynamic}, Status: {status})")
    
    # Print the dependency graph
    print("\nDependency Graph:")
    for task_id in sorted(tool_executor.task_dependencies.keys()):
        deps = tool_executor.task_dependencies[task_id]
        if deps:
            print(f"- {task_id} depends on: {', '.join(deps)}")
        else:
            print(f"- {task_id} has no dependencies")

if __name__ == "__main__":
    # Create the context directory if it doesn't exist
    os.makedirs("context/tasks", exist_ok=True)
    
    # Run the test
    asyncio.run(test_dynamic_task_generation())
