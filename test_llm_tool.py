#!/usr/bin/env python3
"""
Test script for the LLM tool.
"""
import asyncio
import json
from tools.llm_tool_base import LLMToolBase

class TestTool(LLMToolBase):
    """Test tool for LLM."""
    
    def __init__(self):
        super().__init__("test_tool")
    
    async def process(self, *args, **kwargs):
        """Process method required by abstract base class."""
        pass

async def main():
    # Create a test tool
    tool = TestTool()
    
    # Test the generate_content method
    assistant_prompt = "You are a helpful assistant."
    user_prompt = "What is the capital of France?"
    
    try:
        # Generate content using the o4-mini model
        print("Testing with o4-mini model...")
        response = await tool.generate_content(
            assistant_prompt=assistant_prompt,
            user_prompt=user_prompt,
            model="o4-mini"
        )
        print("\nResponse:")
        print(response)
        
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    asyncio.run(main())
