#!/usr/bin/env python3
"""
Test script for parameter processing in the ToolExecutor class.
"""

import os
import sys
import json
import logging

# Add the parent directory to the path so we can import gaiav2 modules
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from gaiav2.execution.tool_executor import ToolExecutor

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def main():
    """Test parameter processing in the ToolExecutor class."""
    # Create a simple execution schedule
    execution_schedule = [
        {"t1", "t2"},  # Level 1
        {"t3", "t4"},  # Level 2
        {"t5"}         # Level 3
    ]
    
    # Create a simple action map
    action_map = {
        "t1": {
            "action": "search_flight",
            "parameters": {
                "from_airport": "ALG",
                "to_airport": "PAR",
                "date": "2025-10-01"
            },
            "name": "Search flights"
        },
        "t2": {
            "action": "search_hotel",
            "parameters": {
                "destination": "Paris",
                "check_in": "2025-10-01",
                "check_out": "2025-10-30"
            },
            "name": "Search hotels"
        },
        "t3": {
            "action": "rank_data",
            "parameters": {
                "input_data": "results_of_t1",
                "criteria": "cost and duration"
            },
            "name": "Rank flights"
        },
        "t4": {
            "action": "rank_data",
            "parameters": {
                "input_data": "results_of_t2",
                "criteria": "cost and rating"
            },
            "name": "Rank hotels"
        },
        "t5": {
            "action": "select_data",
            "parameters": {
                "options": "ranked_flights_from_t3",
                "criteria": "lowest cost and acceptable duration"
            },
            "name": "Select best flight"
        }
    }
    
    # Create a ToolExecutor instance
    executor = ToolExecutor(execution_schedule, action_map)
    
    # Add some test results
    executor.results = {
        "results_of_t1": {
            "flights": [
                {"name": "Air France", "price": "$500", "duration": "2h"},
                {"name": "Lufthansa", "price": "$600", "duration": "3h"},
                {"name": "British Airways", "price": "$700", "duration": "4h"}
            ]
        },
        "results_of_t2": {
            "hotels": [
                {"name": "Hotel A", "price": "$100", "rating": 3},
                {"name": "Hotel B", "price": "$200", "rating": 4},
                {"name": "Hotel C", "price": "$300", "rating": 5}
            ]
        },
        "results_of_t3": {
            "ranked_items": [
                {"rank": 1, "item": {"name": "Air France", "price": "$500", "duration": "2h"}},
                {"rank": 2, "item": {"name": "Lufthansa", "price": "$600", "duration": "3h"}},
                {"rank": 3, "item": {"name": "British Airways", "price": "$700", "duration": "4h"}}
            ]
        },
        "results_of_t4": {
            "ranked_items": [
                {"rank": 1, "item": {"name": "Hotel C", "price": "$300", "rating": 5}},
                {"rank": 2, "item": {"name": "Hotel B", "price": "$200", "rating": 4}},
                {"rank": 3, "item": {"name": "Hotel A", "price": "$100", "rating": 3}}
            ]
        }
    }
    
    # Test parameter processing for different reference formats
    test_references = [
        "results_of_t1",
        "output_of_t1",
        "flights_from_t1",
        "ranked_items_from_t3",
        "ranked_flights_from_t3",
        "hotels_from_t2",
        "ranked_hotels_from_t4"
    ]
    
    print("Testing parameter processing for different reference formats:")
    for ref in test_references:
        result = executor._process_string_reference(ref)
        print(f"\nReference: {ref}")
        print(f"Result type: {type(result)}")
        print(f"Result: {json.dumps(result, indent=2) if result else 'None'}")
    
    # Test parameter processing for a complete parameter dictionary
    test_params = {
        "options": "ranked_flights_from_t3",
        "criteria": "lowest cost and acceptable duration",
        "nested": {
            "data": "results_of_t1",
            "more_data": "output_of_t2"
        },
        "list_data": ["results_of_t3", "results_of_t4"]
    }
    
    print("\nTesting parameter processing for a complete parameter dictionary:")
    processed_params = executor._process_parameters(test_params)
    print(f"Processed parameters: {json.dumps(processed_params, indent=2)}")

if __name__ == "__main__":
    main()
