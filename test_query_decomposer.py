from planners.QueryDecomposer import QueryDecomposer
from llm import LLM
import asyncio


async def main():
    # Initialize LLM (adjust provider or key via env vars as needed)
    llm = LLM(provider="openrouter")

    # Create supporting ToolSelector

    # Instantiate QueryDecomposer and inject the ToolSelector dependency
    decomposer = QueryDecomposer(llm)

    query = (
        "can you do my grocery shopping list where i can them, the stores need to be at least 5km from my location"
    )

    result = await decomposer.decompose_query(query)
    print("\nDecomposition result:\n", result)


if __name__ == "__main__":
    asyncio.run(main()) 