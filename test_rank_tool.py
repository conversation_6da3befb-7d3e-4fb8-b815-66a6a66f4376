#!/usr/bin/env python3
"""
Test script for the rank_data tool.
"""
import asyncio
import json
from tools.rank_data import rank_data

async def main():
    # Example input data
    example_data = [
        {
            "name": "Tokyo",
            "country": "Japan",
            "population": 37.4,
            "attractions": ["Tokyo Tower", "Shibuya Crossing", "Meiji Shrine"],
            "cuisine": "Japanese",
            "safety_index": 92
        },
        {
            "name": "Paris",
            "country": "France",
            "population": 11.1,
            "attractions": ["Eiffel Tower", "Louvre Museum", "Notre-Dame"],
            "cuisine": "French",
            "safety_index": 78
        },
        {
            "name": "New York",
            "country": "USA",
            "population": 18.8,
            "attractions": ["Statue of Liberty", "Times Square", "Central Park"],
            "cuisine": "American",
            "safety_index": 70
        }
    ]

    # Example criteria
    example_criteria = "Rank cities based on safety, cultural attractions, and food quality."

    # Example prompt
    example_prompt = "Identify the best cities for tourists and rank them."

    try:
        # Call the rank_data function
        result = await rank_data(
            prompt=example_prompt,
            input_data=example_data,
            criteria=example_criteria
        )

        # Print the result
        print(json.dumps(result, indent=2))

    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    asyncio.run(main())
