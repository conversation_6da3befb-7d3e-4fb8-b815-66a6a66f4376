from planners.tool_selector import ToolSelector
from llm import LLM
import os
import asyncio


prompt = """
my grocery shopping list is 
- shrimps
- steak
- butter
- tomato
- potato

find from which store should i buy all of them within a ditance of 5 miles tops from my current location which is 5th ave, austin, TX.

"""

async def main():
    llm = LLM(provider="openai")
    tool_selector = ToolSelector(llm)
    result = await tool_selector.select_tools(prompt, model="o3")
    print(result)

if __name__ == "__main__":
    asyncio.run(main())