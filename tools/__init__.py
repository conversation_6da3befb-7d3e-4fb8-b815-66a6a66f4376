"""
Tools package for GaiaV2.

This package provides a unified interface to all available tools:
- serp: Search engine results (Google, Bing, Brave)
- amazon: Amazon product search
- aldi: Aldi product search
- cars: Car search
- costco: Costco product search
- etsy: Etsy product search
- home_depot: Home Depot product search
- immoweb: Immoweb property search
- lidl: Lidl product search
- nike: Nike product search
- walmart: Walmart product search
- hm: H&M fashion search
- ikea: IKEA furniture search
- carrefour: Carrefour product search
- manomano: ManoMano tools and DIY search
- leroymerlin: <PERSON> DIY and home improvement search
- ubereats: UberEats restaurant and food delivery search
- zalando: Zalando fashion and clothing search
- louisvuitton: Louis <PERSON> luxury goods search
- mediamarkt: MediaMarkt electronics search
- zara: Zara fashion and clothing search
- target: Target retail products search
- tjmaxx: TJMaxx discount retail search
- zillow: Zillow real estate and property search
- flights: Flight search
- hotels: Hotel search
- maps: Maps and places search
- youtube: YouTube video search
- images: Image search
- scholar: Google Scholar search
- web: Web search
- generate_final_answers: Generate final answers based on processed data
- generate_structured_data: Generate structured data files from input data
- rank_data: Rank data based on specific criteria
- evaluate_data: Evaluate data, compare options, make judgments
- select_data: Select options based on evaluation or criteria
"""

# Import submodules to make them available at the package level
from . import serp
from . import amazon
from . import aldi
from . import cars
from . import costco
from . import etsy
from . import home_depot
from . import immoweb
from . import lidl
from . import nike
from . import walmart
from . import hm
from . import ikea
from . import carrefour
from . import manomano
from . import leroymerlin
from . import ubereats
from . import zalando
from . import louisvuitton
from . import mediamarkt
from . import zara
from . import target
from . import tjmaxx
from . import zillow
from . import flights
from . import hotels
from . import maps
from . import youtube
from . import images
from . import scholar
from . import web

# Import level 2 tools
from . import generate_final_answers
from . import generate_structured_data
from . import rank_data
from . import evaluate_data
from . import select_data

# For backward compatibility
from .base_tool import BaseTool
from .serp import SerpTool
from .amazon import AmazonTool
from .aldi import AldiTool
from .cars import CarsTool
from .costco import CostcoTool
from .etsy import EtsyTool
from .home_depot import HomeDepotTool
from .immoweb import ImmowebTool
from .lidl import LidlTool
from .nike import NikeTool
from .walmart import WalmartTool
from .hm import HMTool
from .ikea import IkeaTool
from .carrefour import CarrefourTool
from .manomano import ManoManoTool
from .leroymerlin import LeroyMerlinTool
from .ubereats import UberEatsTool
from .zalando import ZalandoTool
from .louisvuitton import LouisVuittonTool
from .mediamarkt import MediaMarktTool
from .zara import ZaraTool
from .target import TargetTool
from .tjmaxx import TJMaxxTool
from .zillow import ZillowTool
from .flights import FlightsTool
from .hotels import HotelsTool
from .maps import MapsTool
from .youtube import YouTubeTool
from .images import ImagesTool
from .scholar import ScholarTool
from .web import WebTool

# Level 2 tool classes
from .generate_final_answers import GenerateFinalAnswersTool
from .generate_structured_data import GenerateStructuredDataTool
from .rank_data import RankDataTool
from .evaluate_data import EvaluateDataTool
from .select_data import SelectDataTool

__all__ = [
    # Submodules
    'serp',
    'amazon',
    'aldi',
    'cars',
    'costco',
    'etsy',
    'home_depot',
    'immoweb',
    'lidl',
    'nike',
    'walmart',
    'hm',
    'ikea',
    'carrefour',
    'manomano',
    'leroymerlin',
    'ubereats',
    'zalando',
    'louisvuitton',
    'mediamarkt',
    'zara',
    'target',
    'tjmaxx',
    'zillow',
    'flights',
    'hotels',
    'maps',
    'youtube',
    'images',
    'scholar',
    'web',
    'generate_final_answers',
    'generate_structured_data',
    'rank_data',
    'evaluate_data',
    'select_data',

    # Classes (for backward compatibility)
    'BaseTool',
    'SerpTool',
    'AmazonTool',
    'AldiTool',
    'CarsTool',
    'CostcoTool',
    'EtsyTool',
    'HomeDepotTool',
    'ImmowebTool',
    'LidlTool',
    'NikeTool',
    'WalmartTool',
    'HMTool',
    'IkeaTool',
    'CarrefourTool',
    'ManoManoTool',
    'LeroyMerlinTool',
    'UberEatsTool',
    'ZalandoTool',
    'LouisVuittonTool',
    'MediaMarktTool',
    'ZaraTool',
    'TargetTool',
    'TJMaxxTool',
    'ZillowTool',
    'FlightsTool',
    'HotelsTool',
    'MapsTool',
    'YouTubeTool',
    'ImagesTool',
    'ScholarTool',
    'WebTool',
    'GenerateFinalAnswersTool',
    'GenerateStructuredDataTool',
    'RankDataTool',
    'EvaluateDataTool',
    'SelectDataTool'
]
