#!/usr/bin/env python3
"""
Amazon product search tool consumer.
"""

import os
from typing import Dict, Any, List, Optional, Union

try:
    # When imported as a module
    from ..base_tool import BaseTool
except ImportError:
    # When run directly
    import sys
    import os
    sys.path.append(os.path.dirname(os.path.dirname(__file__)))
    from base_tool import BaseTool


class AmazonTool(BaseTool):
    """
    Tool for searching Amazon products using the Amazon Product Scraper API.
    """

    def __init__(self,
                base_url: str = None,
                timeout: int = 120,  # Longer timeout as Amazon scraping takes time
                zyte_api_key: Optional[str] = None,
                env_file: str = None):
        """
        Initialize the Amazon search tool.

        Args:
            base_url: Base URL for the Amazon API
            timeout: Request timeout in seconds
            zyte_api_key: API key for Zyte proxy service
            env_file: Path to .env file
        """
        # Get base URL from environment variable if not provided
        if base_url is None:
            base_url = os.environ.get("AMAZON_BASE_URL", "http://localhost:9280")
        
        # Initialize base class (this will load environment variables)
        super().__init__(base_url, timeout, env_file)

        # Get API key from parameter or environment variable
        self.zyte_api_key = zyte_api_key or os.environ.get("ZYTE_API_KEY", "")

    def search(self,
              query: str,
              max_results: int = 10,
              stats_threshold: float = 1.0,
              method: str = "GET",
              **kwargs) -> Dict[str, Any]:
        """
        Search for products on Amazon.

        Args:
            query: Search query for Amazon products
            max_results: Maximum number of results to return
            stats_threshold: Statistical threshold for filtering products
            method: HTTP method to use ("POST" or "GET")
            **kwargs: Additional search parameters

        Returns:
            Amazon product search results
        """
        # Prepare request data
        data = {
            "query": query,
            "max_results": max_results,
            "stats_threshold": stats_threshold
        }

        # Add API key
        if self.zyte_api_key:
            data["zyte_api_key"] = self.zyte_api_key

        # Add any additional parameters
        data.update(kwargs)

        if method.upper() == "POST":
            # Use POST method with JSON body
            return self._make_request("POST", "/scrape", data=data)
        else:
            # Use GET method with URL parameters
            return self._make_request("GET", "/search", params=data)

    def get_product_details(self, product_url: str) -> Dict[str, Any]:
        """
        Get detailed information about a specific product.

        Note: This is a placeholder for future implementation.
        The current API doesn't have a dedicated endpoint for this.

        Args:
            product_url: URL of the product

        Returns:
            Product details
        """
        # This is a placeholder for future implementation
        raise NotImplementedError("Product details endpoint not implemented yet")


# Example usage
if __name__ == "__main__":
    # Create the Amazon search tool
    amazon_tool = AmazonTool()

    try:
        # Check if the API is healthy
        health = amazon_tool.check_health()
        print(f"API Health: {health}")

        # Perform a search using POST method
        results = amazon_tool.search(
            query="samsung tv 4k 55 inch",
            max_results=5,
            method="POST"
        )

        # Print search results
        print(f"\nSearch results for 'samsung tv 4k 55 inch':")
        print(f"Query: {results.get('query')}")

        # Print stats
        stats = results.get('stats', {})
        print(f"Success: {stats.get('Success', 0)}")
        print(f"Errors: {stats.get('Errors', 0)}")
        print(f"Total time: {stats.get('TotalDuration', 0):.2f} seconds")

        # Print products
        products = results.get('products', [])
        print(f"Products found: {len(products)}")

        # Print first few products
        for i, product in enumerate(products[:3]):
            print(f"\nProduct {i+1}:")
            print(f"Title: {product.get('title')}")
            print(f"Price: {product.get('price')}")
            print(f"URL: {product.get('url')}")

            # Print variants if available
            variants = product.get('variants', [])
            if variants:
                print(f"Variants: {len(variants)}")
                for j, variant in enumerate(variants[:2]):
                    print(f"  {j+1}. Size: {variant.get('size', 'N/A')}, Price: {variant.get('price', 'N/A')}")

    except Exception as e:
        print(f"Error: {e}")
