#!/usr/bin/env python3
"""
Carrefour search module.

This module provides a simple interface to the Carrefour search API.
"""

from typing import Dict, Any, Optional
from .carrefour_tool import CarrefourTool

# Global tool instance
_carrefour_tool = None


def get_carrefour_tool(zyte_api_key: Optional[str] = None) -> CarrefourTool:
    """
    Get or create a global Carrefour tool instance.
    
    Args:
        zyte_api_key: Zyte API key for authentication
    
    Returns:
        CarrefourTool: The Carrefour tool instance
    """
    global _carrefour_tool
    if _carrefour_tool is None:
        _carrefour_tool = CarrefourTool(zyte_api_key=zyte_api_key)
    return _carrefour_tool


def search(query: str, zyte_api_key: Optional[str] = None, **kwargs) -> Dict[str, Any]:
    """
    Search for products on Carrefour.
    
    Args:
        query (str): Search query for Carrefour products
        zyte_api_key (str, optional): Zyte API key for authentication
        **kwargs: Additional search parameters
        
    Returns:
        Dict[str, Any]: Search results from Carrefour
    """
    tool = get_carrefour_tool(zyte_api_key)
    return tool.search(query, **kwargs)


def check_health(zyte_api_key: Optional[str] = None) -> Dict[str, Any]:
    """
    Check if the Carrefour service is available.
    
    Args:
        zyte_api_key (str, optional): Zyte API key for authentication
    
    Returns:
        Dict[str, Any]: Health status
    """
    tool = get_carrefour_tool(zyte_api_key)
    return tool.check_health()


def get_health_endpoint(zyte_api_key: Optional[str] = None) -> Dict[str, Any]:
    """
    Check the dedicated health endpoint.
    
    Args:
        zyte_api_key (str, optional): Zyte API key for authentication
    
    Returns:
        Dict[str, Any]: Health endpoint response
    """
    tool = get_carrefour_tool(zyte_api_key)
    return tool.get_health_endpoint()


def get_available_endpoints() -> Dict[str, str]:
    """
    Get available Carrefour API endpoints.
    
    Returns:
        Dict[str, str]: Available endpoints
    """
    return {
        'root': 'GET /',
        'health': 'GET /health',
        'search': 'GET /search?query=<query>&zyte_api_key=<key>'
    }