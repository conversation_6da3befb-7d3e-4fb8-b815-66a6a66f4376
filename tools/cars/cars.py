"""Cars search module.

This module provides a simple interface to the Cars search API.
"""

import os
from typing import Dict, Any, List, Optional

from .cars_tool import CarsTool

# Create a default instance with environment variables
_default_tool = CarsTool()

def search(query: str, 
           **kwargs) -> Dict[str, Any]:
    """
    Search for cars.

    Args:
        query: Search query for cars (e.g., "toyota", "honda civic")
        **kwargs: Additional search parameters

    Returns:
        Car search results
    """
    return _default_tool.search(query=query, **kwargs)

def check_health() -> Dict[str, Any]:
    """
    Check if the Cars API is healthy.

    Returns:
        API health status
    """
    return _default_tool.check_health()

__all__ = ['search', 'check_health']