#!/usr/bin/env python3
"""
Costco product search tool consumer.
"""

import os
from typing import Dict, Any, List, Optional

try:
    # When imported as a module
    from ..base_tool import BaseTool
except ImportError:
    # When run directly
    import sys
    import os
    sys.path.append(os.path.dirname(os.path.dirname(__file__)))
    from base_tool import BaseTool


class CostcoTool(BaseTool):
    """
    Tool for searching Costco products using the Costco Product Scraper API.
    """

    def __init__(self,
                base_url: str = None,
                timeout: int = 120,  # Longer timeout as Costco scraping takes time
                scrapingbee_api_key: Optional[str] = None,
                env_file: str = None):
        """
        Initialize the Costco search tool.

        Args:
            base_url: Base URL for the Costco API
            timeout: Request timeout in seconds
            scrapingbee_api_key: API key for ScrapingBee proxy service
            env_file: Path to .env file
        """
        # Get base URL from environment variable if not provided
        if base_url is None:
            base_url = os.environ.get("COSTCO_BASE_URL", "http://localhost:9291")
        
                # Initialize base class (this will load environment variables)
        super().__init__(base_url, timeout, env_file)

        # Get API key from parameter or environment variable
        self.scrapingbee_api_key = scrapingbee_api_key or os.environ.get("SCRAPINGBEE_API_KEY", "")

    def search(self,
              query: str,
              scrapingbee_api_key: Optional[str] = None,
              **kwargs) -> Dict[str, Any]:
        """
        Search for products on Costco.

        Args:
            query: Search query for Costco products
            scrapingbee_api_key: API key for ScrapingBee proxy service (overrides instance key)
            **kwargs: Additional search parameters

        Returns:
            Costco product search results
        """
        # Prepare request parameters
        params = {
            "query": query
        }

        # Use provided API key or instance key
        api_key = scrapingbee_api_key or self.scrapingbee_api_key
        if api_key:
            params["scrapingbee_api_key"] = api_key

        # Add any additional parameters
        params.update(kwargs)

        # Use GET method with URL parameters
        return self._make_request("GET", "/search", params=params)


# Example usage
if __name__ == "__main__":
    # Create the Costco search tool
    costco_tool = CostcoTool()

    try:
        # Check if the API is healthy
        health = costco_tool.check_health()
        print(f"API Health: {health}")

        # Perform a search
        results = costco_tool.search(
            query="electronics",
            scrapingbee_api_key="********************************************************************************"
        )

        # Print search results
        print(f"\nSearch results for 'electronics':")
        print(f"Query: {results.get('query')}")
        
        # Print products if available
        products = results.get('products', [])
        if products:
            print(f"Products found: {len(products)}")
            
            # Print first few products
            for i, product in enumerate(products[:3]):
                print(f"\nProduct {i+1}:")
                print(f"Title: {product.get('title')}")
                print(f"Price: {product.get('price')}")
                print(f"URL: {product.get('url')}")
        else:
            print("No products found or different response structure")
            print(f"Full response: {results}")

    except Exception as e:
        print(f"Error: {e}")