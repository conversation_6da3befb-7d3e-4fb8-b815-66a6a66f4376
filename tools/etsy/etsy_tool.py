import os
import requests
from typing import Dict, Any, Optional
from ..base_tool import BaseTool


class EtsyTool(BaseTool):
    """
    Tool for searching Etsy products using Zyte API.
    """
    
    def __init__(self, zyte_api_key: Optional[str] = None):
        super().__init__("etsy")
        self.base_url = "http://localhost:9292"
        self.zyte_api_key = zyte_api_key or os.getenv('ZYTE_API_KEY')
    
    def search(self, query: str, **kwargs) -> Dict[str, Any]:
        """
        Search for products on Etsy.
        
        Args:
            query (str): Search query for Etsy products
            **kwargs: Additional parameters
            
        Returns:
            Dict[str, Any]: Search results from Etsy
        """
        try:
            params = {
                'query': query,
                'countries': 'USA',  # Limit to one country for faster response
                'k_limit': 2  # Limit to 2 detailed products for faster response
            }

            if self.zyte_api_key:
                params['zyte_api_key'] = self.zyte_api_key

            response = self.make_request(
                method='GET',
                url=f"{self.base_url}/search",
                params=params,
                timeout=120  # Increase timeout to 2 minutes
            )
            
            return response
            
        except Exception as e:
            return {
                'error': f"Etsy search failed: {str(e)}",
                'query': query
            }
    
    def check_health(self) -> Dict[str, Any]:
        """
        Check if the Etsy service is available.
        
        Returns:
            Dict[str, Any]: Health status
        """
        try:
            response = self.make_request(
                method='GET',
                url=self.base_url
            )
            return {
                'status': 'healthy',
                'service': 'etsy',
                'response': response
            }
        except Exception as e:
            return {
                'status': 'unhealthy',
                'service': 'etsy',
                'error': str(e)
            }


# Example usage
if __name__ == "__main__":
    tool = EtsyTool()
    result = tool.search("handmade jewelry")
    print(result)