"""
Evaluate Data module.

This module provides a simple interface to the Evaluate Data tool.
The tool evaluates data, compares options, and makes judgments based on criteria.

All LLM tools in this package use a standardized output format:
{
  "status": "success|error",
  "tool_name": "evaluate_data",
  "timestamp": "ISO-8601 timestamp",
  "request_id": "unique_request_id",
  "execution_time": seconds,
  "metadata": {
    "criteria_description": "original criteria",
    "evaluation_score": numerical score (0-100),
    "evaluation_rating": "Excellent/Good/Average/Poor",
    "strengths_count": number of strengths identified,
    "weaknesses_count": number of weaknesses identified,
    "recommendations_count": number of recommendations provided,
    "model_used": "model name"
  },
  "data": {
    "summary": "brief overview of the evaluation",
    "detailed_analysis": {
      "component1": "analysis of component1",
      "component2": "analysis of component2",
      ...
    },
    "strengths": ["strength1", "strength2", ...],
    "weaknesses": ["weakness1", "weakness2", ...],
    "recommendations": ["recommendation1", "recommendation2", ...],
    "overall_assessment": {
      "score": numerical score (0-100),
      "rating": "Excellent/Good/Average/Poor",
      "conclusion": "final conclusion statement"
    }
  }
}
"""

from typing import Dict, Any, Union

from .evaluate_data_tool import EvaluateDataTool

# Create a default instance
_default_tool = EvaluateDataTool()

async def evaluate_data(input_data: Any, criteria: Union[str, Dict]) -> Dict[str, Any]:
    """
    Evaluate data, compare options, and make judgments.

    Args:
        input_data: Any input data to evaluate
        criteria: String description or dictionary of evaluation criteria

    Returns:
        Standardized output dictionary with the following structure:
        {
          "status": "success|error",
          "tool_name": "evaluate_data",
          "timestamp": "ISO-8601 timestamp",
          "request_id": "unique_request_id",
          "execution_time": seconds,
          "metadata": {
            "criteria_description": "original criteria",
            "evaluation_score": numerical score (0-100),
            "evaluation_rating": "Excellent/Good/Average/Poor",
            "strengths_count": number of strengths identified,
            "weaknesses_count": number of weaknesses identified,
            "recommendations_count": number of recommendations provided,
            "model_used": "model name"
          },
          "data": {
            "summary": "brief overview of the evaluation",
            "detailed_analysis": {
              "component1": "analysis of component1",
              "component2": "analysis of component2",
              ...
            },
            "strengths": ["strength1", "strength2", ...],
            "weaknesses": ["weakness1", "weakness2", ...],
            "recommendations": ["recommendation1", "recommendation2", ...],
            "overall_assessment": {
              "score": numerical score (0-100),
              "rating": "Excellent/Good/Average/Poor",
              "conclusion": "final conclusion statement"
            }
          }
        }
    """
    return await _default_tool.evaluate_data(input_data, criteria)


__all__ = ['evaluate_data']