#!/usr/bin/env python3
"""
Script to fix all hardcoded base URLs in tool files to use localhost instead of localhost
"""

import os
import re
from pathlib import Path

def fix_base_url_in_file(file_path):
    """
    Fix hardcoded base URLs in a tool file.
    """
    with open(file_path, 'r') as f:
        content = f.read()
    
    original_content = content
    
    # Replace all occurrences of localhost with localhost
    content = content.replace('localhost', 'localhost')

    # Also fix any remaining hardcoded IPs that might be different
    # Pattern to match IP addresses in base URL fallbacks within strings
    ip_pattern = r'"http://(\d+\.\d+\.\d+\.\d+):(\d+)"'

    def replace_ip_in_string(match):
        ip = match.group(1)
        port = match.group(2)
        # Only replace if it's not already localhost
        if ip != 'localhost' and ip != '127.0.0.1':
            return f'"http://localhost:{port}"'
        return match.group(0)

    content = re.sub(ip_pattern, replace_ip_in_string, content)

    # Also handle cases without quotes
    ip_pattern2 = r'http://(\d+\.\d+\.\d+\.\d+):(\d+)'

    def replace_ip_general(match):
        ip = match.group(1)
        port = match.group(2)
        # Only replace if it's not already localhost
        if ip != 'localhost' and ip != '127.0.0.1':
            return f'http://localhost:{port}'
        return match.group(0)

    content = re.sub(ip_pattern2, replace_ip_general, content)
    
    if content != original_content:
        with open(file_path, 'w') as f:
            f.write(content)
        print(f"Fixed base URLs in: {file_path}")
        return True
    
    return False

def main():
    """
    Main function to fix all tool files.
    """
    tools_dir = Path(__file__).parent
    
    # Find all tool files
    tool_files = []
    
    # Look for *_tool.py files in subdirectories
    for tool_dir in tools_dir.iterdir():
        if tool_dir.is_dir() and not tool_dir.name.startswith('.') and not tool_dir.name.startswith('__'):
            tool_file = tool_dir / f"{tool_dir.name}_tool.py"
            if tool_file.exists():
                tool_files.append(tool_file)
    
    # Also check for any other Python files that might have hardcoded URLs
    for pattern in ['*/*.py', '*.py']:
        tool_files.extend(tools_dir.glob(pattern))
    
    # Remove duplicates
    tool_files = list(set(tool_files))
    
    fixed_count = 0
    
    print(f"Checking {len(tool_files)} files for hardcoded base URLs...")
    
    for tool_file in tool_files:
        try:
            if fix_base_url_in_file(tool_file):
                fixed_count += 1
        except Exception as e:
            print(f"Error processing {tool_file}: {e}")
    
    print(f"\nFixed {fixed_count} files")
    
    # Also check and fix the README files
    readme_files = list(tools_dir.glob('*.md'))
    readme_fixed = 0
    
    for readme_file in readme_files:
        try:
            if fix_base_url_in_file(readme_file):
                readme_fixed += 1
        except Exception as e:
            print(f"Error processing {readme_file}: {e}")
    
    if readme_fixed > 0:
        print(f"Fixed {readme_fixed} README files")

if __name__ == "__main__":
    main()
