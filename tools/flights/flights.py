"""
Flight search module.

This module provides a simple interface to the Flight search API.
"""

from typing import Dict, Any, Optional

from .flights_tool import FlightsTool

# Create a default instance with environment variables
_default_tool = FlightsTool()

def search(from_airport: str,
           to_airport: str,
           date: str,
           return_date: Optional[str] = None,
           trip_type: str = "one-way",
           adults: int = 1,
           children: int = 0,
           infants_in_seat: int = 0,
           infants_on_lap: int = 0,
           seat_type: str = "economy",
           max_stops: int = 5,
           max_results: int = 50,
           **kwargs) -> Dict[str, Any]:
    """
    Search for flights.

    Args:
        from_airport: Departure airport code (e.g., 'JFK')
        to_airport: Arrival airport code (e.g., 'LAX')
        date: Date of departure (YYYY-MM-DD)
        return_date: Date of return for round trips (YYYY-MM-DD)
        trip_type: Trip type: 'one-way' or 'round-trip'
        adults: Number of adult passengers
        children: Number of child passengers
        infants_in_seat: Number of infants with seats
        infants_on_lap: Number of infants on lap
        seat_type: Seat type: 'economy', 'premium_economy', 'business', 'first'
        max_stops: Maximum number of stops
        max_results: Maximum number of results to return
        **kwargs: Additional search parameters

    Returns:
        Flight search results
    """
    return _default_tool.search(
        from_airport=from_airport,
        to_airport=to_airport,
        date=date,
        return_date=return_date,
        trip=trip_type,
        adults=adults,
        children=children,
        infants_in_seat=infants_in_seat,
        infants_on_lap=infants_on_lap,
        seat=seat_type,
        max_stops=max_stops,
        max_results=max_results,
        **kwargs
    )

def check_health() -> Dict[str, Any]:
    """
    Check if the Flights API is healthy.

    Returns:
        API health status
    """
    return _default_tool.check_health()


__all__ = ['search', 'check_health']