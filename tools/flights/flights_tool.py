#!/usr/bin/env python3
"""
Flight search tool consumer.
"""

import os
from datetime import datetime
from typing import Dict, Any, List, Optional, Union

try:
    # When imported as a module
    from ..base_tool import BaseTool
except ImportError:
    # When run directly
    import sys
    import os
    sys.path.append(os.path.dirname(os.path.dirname(__file__)))
    from base_tool import BaseTool


class FlightsTool(BaseTool):
    """
    Tool for searching flights using the Flight Search API.
    """

    def __init__(self,
                base_url: str = None,
                timeout: int = 60,
                env_file: str = None):
        """
        Initialize the Flight search tool.

        Args:
            base_url: Base URL for the Flight API
            timeout: Request timeout in seconds
            env_file: Path to .env file
        """
        # Get base URL from environment variable if not provided
        if base_url is None:
            base_url = os.environ.get("FLIGHTS_BASE_URL", "http://localhost:9282")
        
                # Initialize base class (this will load environment variables)
        super().__init__(base_url, timeout, env_file)

    def search(self,
              from_airport: str,
              to_airport: str,
              date: str,
              return_date: Optional[str] = None,
              trip: str = "one-way",
              adults: int = 1,
              children: int = 0,
              infants_in_seat: int = 0,
              infants_on_lap: int = 0,
              **kwargs) -> Dict[str, Any]:
        """
        Search for flights.

        Args:
            from_airport: Departure airport code (e.g., 'JFK')
            to_airport: Arrival airport code (e.g., 'LAX')
            date: Date of departure (YYYY-MM-DD)
            return_date: Date of return for round trips (YYYY-MM-DD)
            trip: Trip type: 'one-way' or 'round-trip'
            adults: Number of adult passengers
            children: Number of child passengers
            infants_in_seat: Number of infants with seats
            infants_on_lap: Number of infants on lap
            **kwargs: Additional search parameters

        Returns:
            Flight search results
        """
        # Validate date format
        try:
            datetime.strptime(date, "%Y-%m-%d")
            if return_date:
                datetime.strptime(return_date, "%Y-%m-%d")
        except ValueError:
            raise ValueError("Dates must be in YYYY-MM-DD format")

        # Prepare request parameters
        params = {
            "from_airport": from_airport,
            "to_airport": to_airport,
            "date": date,
            "trip": trip,
            "adults": adults,
            "children": children,
            "infants_in_seat": infants_in_seat,
            "infants_on_lap": infants_on_lap
        }

        if params['trip'][0] == 'r':
            params['trip'] = 'round-trip'
        elif params['trip'][0] == 'o':
            params['trip'] = 'one-way'

        # Add return date if provided
        if return_date:
            params["return_date"] = return_date

        # Add any additional parameters
        params.update(kwargs)

        # Make the request
        return self._make_request("GET", "/flights/search", params=params)


# Example usage
if __name__ == "__main__":
    # Create the Flight search tool
    flights_tool = FlightsTool()

    try:
        # Check if the API is healthy
        health = flights_tool.check_health()
        print(f"API Health: {health}")

        # Perform a search
        results = flights_tool.search(
            from_airport="JFK",
            to_airport="LAX",
            date="2025-12-01",
            return_date="2025-12-10",
            trip="one-way",
            adults=2
        )

        # Print search results
        print(f"\nSearch results for flights from JFK to LAX:")

        # Print flights
        flights = results.get('flights', [])
        print(f"Flights found: {len(flights)}")

        # Print first few flights
        for i, flight in enumerate(flights[:3]):
            print(f"\nFlight {i+1}:")
            print(f"Airline: {flight.get('airline')}")
            print(f"Price: {flight.get('price')}")
            print(f"Duration: {flight.get('duration')}")
            print(f"Stops: {flight.get('stops')}")

    except Exception as e:
        print(f"Error: {e}")
