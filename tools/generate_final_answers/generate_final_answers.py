"""
Generate Final Answers module.

This module provides a simple interface to the Generate Final Answers tool.
The tool generates comprehensive, well-structured answers based on processed data
and the original user question.

All LLM tools in this package use a standardized output format:
{
  "status": "success|error",
  "tool_name": "generate_final_answers",
  "timestamp": "ISO-8601 timestamp",
  "request_id": "unique_request_id",
  "execution_time": seconds,
  "metadata": {
    "original_question": "user question",
    "format": "markdown|json|text",
    "model_used": "model name"
  },
  "data": {
    "answer": "full generated answer",
    "sections": [{"title": "section title", "content": "section content"}, ...],
    "summary": "brief summary",
    "recommendations": ["recommendation 1", ...]
  }
}
"""

from typing import Dict, Any

from .generate_final_answers_tool import GenerateFinalAnswersTool

# Create a default instance
_default_tool = GenerateFinalAnswersTool()

async def generate_final_answers(processed_data: Any, original_question: str = None, format: str = "markdown") -> Dict[str, Any]:
    """
    Generate final answers based on processed data and the original user question.

    Args:
        processed_data: Any processed data to generate answers from
        original_question: The original question asked by the user (optional)
        format: Output format (markdown, json, text)

    Returns:
        Standardized output dictionary with the following structure:
        {
          "status": "success|error",
          "tool_name": "generate_final_answers",
          "timestamp": "ISO-8601 timestamp",
          "request_id": "unique_request_id",
          "execution_time": seconds,
          "metadata": {
            "original_question": "user question",
            "format": "markdown|json|text",
            "model_used": "model name"
          },
          "data": {
            "answer": "full generated answer",
            "sections": [{"title": "section title", "content": "section content"}, ...],
            "summary": "brief summary",
            "recommendations": ["recommendation 1", ...]
          }
        }
    """
    return await _default_tool.generate_final_answers(processed_data, original_question, format)


__all__ = []