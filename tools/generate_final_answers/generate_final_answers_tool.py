#!/usr/bin/env python3
"""
Generate Final Answers Tool.

This tool generates final answers based on processed data using LLM.
It supports streaming the generated content in real-time.
"""

import json
import re
import sys
from typing import Dict, Any, List, Tuple

try:
    # When imported as a module
    from ..llm.base import <PERSON><PERSON>rovider
    from ..base_tool import BaseTool
except ImportError:
    # When run directly
    import sys
    import os
    sys.path.append(os.path.dirname(os.path.dirname(__file__)))
    from llm.base import LLMProvider
    from base_tool import BaseTool


class GenerateFinalAnswersTool:
    """
    Tool for generating final answers based on processed data using LLM.

    This tool:
    1. Takes processed data and an optional original question
    2. Generates a comprehensive, well-structured answer
    3. Streams the answer in real-time as it's being generated
    4. Extracts sections, summary, and recommendations from the answer
    5. Returns a standardized output with the answer and metadata
    """

    def __init__(self, env_file: str = None):
        """Initialize the Generate Final Answers tool."""
        self.env_file = env_file

    async def process(self, processed_data: Any, original_question: str = None, format: str = "markdown") -> Dict[str, Any]:
        """
        Process the input data and generate a final answer.

        Args:
            processed_data: Any processed data to generate answers from
            original_question: The original question asked by the user (optional)
            format: Output format (markdown, json, text)

        Returns:
            Standardized output dictionary
        """
        return await self.execute_tool(
            self._generate_final_answers,
            processed_data=processed_data,
            original_question=original_question,
            format=format
        )

    async def generate_final_answers(self, processed_data: Any, original_question: str = None, format: str = "markdown") -> Dict[str, Any]:
        """
        Generate final answers based on processed data and the original user question.

        Args:
            processed_data: Any processed data to generate answers from
            original_question: The original question asked by the user (optional)
            format: Output format (markdown, json, text)

        Returns:
            Standardized output dictionary
        """
        return await self.process(processed_data, original_question, format)

    async def _generate_final_answers(self, processed_data: Any, original_question: str = None, format: str = "markdown") -> Tuple[Dict[str, Any], Dict[str, Any]]:
        """
        Internal implementation of generate_final_answers.

        Args:
            processed_data: Any processed data to generate answers from
            original_question: The original question asked by the user (optional)
            format: Output format (markdown, json, text)

        Returns:
            Tuple of (data, metadata)
        """
        # Prepare the data and prompts
        data_str = self._prepare_data_string(processed_data)
        assistant_prompt, user_prompt = self._create_prompts(data_str, original_question, format)

        # Stream the content and collect the full response
        generated_content = await self._stream_content(assistant_prompt, user_prompt)

        # Extract sections, summary, and recommendations
        sections = self._extract_sections(generated_content)
        summary = self._extract_summary(generated_content)
        recommendations = self._extract_recommendations(generated_content)

        # Prepare and return the data and metadata
        return self._prepare_result(generated_content, sections, summary, recommendations, original_question, format)

    def _prepare_data_string(self, processed_data: Any) -> str:
        """Convert processed data to a string format."""
        if isinstance(processed_data, (dict, list)):
            return json.dumps(processed_data, indent=2)
        return str(processed_data)

    def _create_prompts(self, data_str: str, original_question: str = None, format: str = "markdown") -> Tuple[str, str]:
        """Create the assistant and user prompts."""
        # Create the assistant prompt
        assistant_prompt = "I am an AI assistant that helps generate comprehensive, well-structured final answers based on processed data and user questions."

        # Create the user prompt, including the original question if provided
        question_context = f"ORIGINAL QUESTION:\n{original_question}\n\n" if original_question else ""

        user_prompt = f"""
                        {question_context}Please analyze the following data and generate a comprehensive final answer.
                        The answer should be well-structured, informative, and directly address the main points in the data.
                        {f"Make sure your answer specifically addresses the original question: '{original_question}'" if original_question else ""}

                        DATA:
                        {data_str}

                        Your answer should:
                        1. Be clear, concise, and directly relevant to the user's needs
                        2. Highlight the most important information first
                        3. Include specific details from the data that support your conclusions
                        4. Provide actionable recommendations when appropriate
                        5. Be organized with appropriate headings and structure

                        In addition to the main content, please also provide:
                        - A brief summary (1-2 sentences)
                        - 2-3 key recommendations or actionable insights
                        - Section headings that organize your response

                        Format your main response in {format} format.
                        """

        return assistant_prompt, user_prompt

    async def _stream_content(self, assistant_prompt: str, user_prompt: str) -> str:
        """Stream content from the LLM and return the full response."""
        import sys

        # Initialize an empty string to collect the full response
        generated_content = ""

        # Get messages for the LLM
        messages = [
            {"role": "assistant", "content": assistant_prompt},
            {"role": "user", "content": user_prompt}
        ]

        # Stream the content and print it in real-time
        print("\nGenerating final answer...", file=sys.stderr)
        async for chunk in self.llm.stream_generate(
            messages=messages,
            model=self.default_model
        ):
            # Print the chunk to stdout without buffering
            print(chunk, end="", flush=True)
            # Append the chunk to the full response
            generated_content += chunk

        return generated_content

    def _prepare_result(self, generated_content: str, sections: List[Dict[str, str]],
                       summary: str, recommendations: List[str],
                       original_question: str = None, format: str = "markdown") -> Tuple[Dict[str, Any], Dict[str, Any]]:
        """Prepare the final result data and metadata."""
        # Prepare the data output
        data = {
            "answer": generated_content,
            "sections": sections,
            "summary": summary,
            "recommendations": recommendations
        }

        # Prepare metadata
        metadata = {
            "original_question": original_question,
            "format": format,
            "model_used": self.default_model
        }

        return data, metadata

    def _extract_sections(self, content: str) -> List[Dict[str, str]]:
        """Extract sections from the generated content based on markdown headings."""
        sections = []

        # Check if there are any headings
        if '##' not in content and '#' not in content:
            return sections

        # Extract sections using regex
        heading_pattern = r'(^|\n)#{1,2}\s+(.+?)($|\n)'
        matches = list(re.finditer(heading_pattern, content))

        # Process each heading and its content
        for i, match in enumerate(matches):
            title = match.group(2).strip()
            start_pos = match.end()

            # Find section end (next heading or end of content)
            end_pos = matches[i+1].start() if i < len(matches) - 1 else len(content)

            # Extract and add the section
            section_content = content[start_pos:end_pos].strip()
            sections.append({
                "title": title,
                "content": section_content
            })

        return sections

    def _extract_summary(self, content: str) -> str:
        """Extract a summary from the generated content."""
        # Try to find a dedicated summary section
        if "summary" in content.lower():
            summary_pattern = r'(?:^|\n)(?:#{1,3}\s+)?summary(?:\s+|:|\n)(.*?)(?:\n\n|\n#|$)'
            match = re.search(summary_pattern, content.lower())
            if match:
                return match.group(1).strip()

        # Fall back to using the first non-heading paragraph
        for paragraph in content.split('\n\n'):
            if not paragraph.startswith('#') and paragraph.strip():
                return paragraph.strip()

        return ""

    def _extract_recommendations(self, content: str) -> List[str]:
        """Extract recommendations from the generated content."""
        # Check if there's a recommendations section
        if "recommend" not in content.lower() and "action" not in content.lower():
            return []

        # Find the recommendations section
        rec_pattern = r'(?:^|\n)(?:#{1,3}\s+)?(?:recommend|action).*?(?:\n|:)(.*?)(?:\n\n|\n#|$)'
        match = re.search(rec_pattern, content.lower(), re.DOTALL)
        if not match:
            return []

        rec_section = match.group(1).strip()

        # Extract list items from the section
        list_items = re.findall(r'(?:^|\n)(?:\d+\.\s+|\*\s+|-\s+)(.+?)(?:\n|$)', rec_section)
        if list_items:
            return [item.strip() for item in list_items]

        # If no list items found, use the whole section as one recommendation
        return [rec_section]


# Example usage
if __name__ == "__main__":
    import asyncio

    async def main():
        """Run example usage of the Generate Final Answers tool."""
        # Create the tool
        tool = GenerateFinalAnswersTool()

        # Example data
        example_data = {
            "flights": [
                {"airline": "Delta", "departure": "2023-12-01 08:00", "arrival": "2023-12-01 12:00", "price": "$350"},
                {"airline": "United", "departure": "2023-12-01 10:00", "arrival": "2023-12-01 14:00", "price": "$320"}
            ],
            "hotels": [
                {"name": "Grand Hotel", "price": "$200/night", "rating": 4.5},
                {"name": "Budget Inn", "price": "$120/night", "rating": 3.8}
            ]
        }

        # Example with a specific question
        question = "What are the best flight and hotel options for my trip in December?"
        print(f"\nOriginal Question: {question}")

        # Generate the answer (this will stream the output in real-time)
        result = await tool.generate_final_answers(example_data, question)

        # Print the result metadata
        print("\nResult Metadata:")
        print(f"Status: {result['status']}")
        print(f"Tool: {result['tool_name']}")
        print(f"Request ID: {result['request_id']}")
        print(f"Execution Time: {result['execution_time']:.2f} seconds")
        print(f"Model Used: {result['metadata']['model_used']}")

        # Print the extracted components
        if result["status"] == "success":
            print("\nExtracted Summary:")
            print(result["data"]["summary"])

            if result["data"]["recommendations"]:
                print("\nExtracted Recommendations:")
                for i, rec in enumerate(result["data"]["recommendations"], 1):
                    print(f"{i}. {rec}")

            if result["data"]["sections"]:
                print("\nExtracted Sections:")
                for section in result["data"]["sections"]:
                    print(f"- {section['title']}")

    # Run the example
    asyncio.run(main())
