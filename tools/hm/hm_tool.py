#!/usr/bin/env python3
"""
H&M search tool consumer.
"""

import os
from typing import Dict, Any, Optional

try:
    # When imported as a module
    from ..base_tool import BaseTool
except ImportError:
    # When run directly
    import sys
    import os
    sys.path.append(os.path.dirname(os.path.dirname(__file__)))
    from base_tool import BaseTool


class HMTool(BaseTool):
    """
    Tool for searching H&M products using Zyte API.
    """

    def __init__(self,
                zyte_api_key: Optional[str] = None,
                base_url: str = None,
                timeout: int = 300,
                env_file: str = None):
        """
        Initialize the H&M search tool.

        Args:
            zyte_api_key: Zyte API key for authentication
            base_url: Base URL for the H&M API
            timeout: Request timeout in seconds
            env_file: Path to .env file
        """
        # Get base URL from environment variable if not provided
        if base_url is None:
            base_url = os.environ.get("HM_BASE_URL", "http://localhost:9302")
        
                # Initialize base class (this will load environment variables)
        super().__init__(base_url, timeout, env_file)
        self.zyte_api_key = zyte_api_key or os.getenv('ZYTE_API_KEY')

    def search(self, query: str, countries: Optional[str] = None, **kwargs) -> Dict[str, Any]:
        """
        Search for products on H&M.
        
        Args:
            query (str): Search query for H&M products
            countries (str, optional): Countries filter (e.g., "🇬🇧 United Kingdom")
            **kwargs: Additional parameters
            
        Returns:
            Dict[str, Any]: Search results from H&M
        """
        try:
            params = {
                'query': query,
                'countries': countries or '🇺🇸 United States'  # Default to US for faster response
            }

            if self.zyte_api_key:
                params['zyte_api_key'] = self.zyte_api_key

            # Add any additional parameters
            params.update(kwargs)

            response = self._make_request(
                method="GET",
                endpoint="/search",
                params=params
            )
            
            return response
            
        except Exception as e:
            return {
                'error': f"H&M search failed: {str(e)}",
                'query': query
            }
    
    def check_health(self) -> Dict[str, Any]:
        """
        Check if the H&M service is available.
        
        Returns:
            Dict[str, Any]: Health status
        """
        try:
            response = self._make_request(
                method="GET",
                endpoint="/"
            )
            return {
                'status': 'healthy',
                'service': 'hm',
                'response': response
            }
        except Exception as e:
            return {
                'status': 'unhealthy',
                'service': 'hm',
                'error': str(e)
            }
    
    def get_health_endpoint(self) -> Dict[str, Any]:
        """
        Check the dedicated health endpoint.
        
        Returns:
            Dict[str, Any]: Health endpoint response
        """
        try:
            response = self._make_request(
                method="GET",
                endpoint="/health"
            )
            return response
        except Exception as e:
            return {
                'error': f"Health endpoint failed: {str(e)}"
            }


# Example usage
if __name__ == "__main__":
    tool = HMTool()
    
    try:
        # Check if the API is healthy
        health = tool.check_health()
        print(f"API Health: {health}")
        
        # Check dedicated health endpoint
        health_endpoint = tool.get_health_endpoint()
        print(f"Health Endpoint: {health_endpoint}")
        
        # Perform a search
        result = tool.search("fashion", countries="🇬🇧 United Kingdom")
        print(f"\nSearch results for 'fashion':")
        print(result)
        
    except Exception as e:
        print(f"Error: {e}")