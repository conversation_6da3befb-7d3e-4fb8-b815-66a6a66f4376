#!/usr/bin/env python3
"""
Hotel search tool consumer.
"""

import os
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Union

try:
    # When imported as a module
    from ..base_tool import BaseTool
except ImportError:
    # When run directly
    import sys
    import os
    sys.path.append(os.path.dirname(os.path.dirname(__file__)))
    from base_tool import BaseTool


class HotelsTool(BaseTool):
    """
    Tool for searching hotels using the Hotel Search API.
    """

    def __init__(self,
                base_url: str = None,
                timeout: int = 60,
                env_file: str = None):
        """
        Initialize the Hotel search tool.

        Args:
            base_url: Base URL for the Hotel API
            timeout: Request timeout in seconds
            env_file: Path to .env file
        """
        # Get base URL from environment variable if not provided
        if base_url is None:
            base_url = os.environ.get("HOTELS_BASE_URL", "http://localhost:9283")
        
                # Initialize base class (this will load environment variables)
        super().__init__(base_url, timeout, env_file)

    def search(self,
              destination: str,
              check_in: str,
              check_out: str,
              adults: int = 1,
              children: int = 0,
              currency: str = "USD",
              include_providers: bool = False,
              debug: bool = False,
              **kwargs) -> Dict[str, Any]:
        """
        Search for hotels.

        Args:
            destination: Destination (city, region, etc.)
            check_in: Check-in date (YYYY-MM-DD)
            check_out: Check-out date (YYYY-MM-DD)
            adults: Number of adults
            children: Number of children
            currency: Currency code (e.g., USD, EUR)
            include_providers: Include provider details for each hotel
            debug: Enable debug mode
            **kwargs: Additional search parameters

        Returns:
            Hotel search results
        """
        # Validate date format
        try:
            datetime.strptime(check_in, "%Y-%m-%d")
            datetime.strptime(check_out, "%Y-%m-%d")
        except ValueError:
            raise ValueError("Dates must be in YYYY-MM-DD format")

        # Prepare request parameters
        params = {
            "destination": destination,
            "check_in": check_in,
            "check_out": check_out,
            "adults": adults,
            "children": children,
            "currency": currency,
            "include_providers": include_providers,
            "debug": debug
        }

        # Add any additional parameters
        params.update(kwargs)

        # Make the request
        return self._make_request("GET", "/hotels/search", params=params)

    def get_hotel_details(self,
                         hotel_id: str,
                         check_in: str,
                         check_out: str,
                         adults: int = 1,
                         children: int = 0,
                         currency: str = "USD",
                         debug: bool = False,
                         **kwargs) -> Dict[str, Any]:
        """
        Get detailed information about a specific hotel.

        Args:
            hotel_id: Hotel ID
            check_in: Check-in date (YYYY-MM-DD)
            check_out: Check-out date (YYYY-MM-DD)
            adults: Number of adults
            children: Number of children
            currency: Currency code (e.g., USD, EUR)
            debug: Enable debug mode
            **kwargs: Additional parameters

        Returns:
            Hotel details
        """
        # Validate date format
        try:
            datetime.strptime(check_in, "%Y-%m-%d")
            datetime.strptime(check_out, "%Y-%m-%d")
        except ValueError:
            raise ValueError("Dates must be in YYYY-MM-DD format")

        # Prepare request parameters
        params = {
            "check_in": check_in,
            "check_out": check_out,
            "adults": adults,
            "children": children,
            "currency": currency,
            "debug": debug
        }

        # Add any additional parameters
        params.update(kwargs)

        # Make the request
        return self._make_request("GET", f"/hotels/details/{hotel_id}", params=params)

    def get_providers(self,
                     booking_link: str,
                     debug: bool = False,
                     **kwargs) -> Dict[str, Any]:
        """
        Get booking providers for a hotel.

        Args:
            booking_link: Hotel booking link
            debug: Enable debug mode
            **kwargs: Additional parameters

        Returns:
            Booking providers
        """
        # Prepare request parameters
        params = {
            "booking_link": booking_link,
            "debug": debug
        }

        # Add any additional parameters
        params.update(kwargs)

        # Make the request
        return self._make_request("GET", "/hotels/providers", params=params)


# Example usage
if __name__ == "__main__":
    # Create the Hotel search tool
    hotels_tool = HotelsTool()

    try:
        # Check if the API is healthy
        health = hotels_tool.check_health()
        print(f"API Health: {health}")

        # Get current date and future date for demo
        today = datetime.now()
        check_in = (today.replace(day=1) + timedelta(days=30)).strftime("%Y-%m-%d")
        check_out = (today.replace(day=1) + timedelta(days=33)).strftime("%Y-%m-%d")

        # Perform a search
        results = hotels_tool.search(
            destination="New York",
            check_in=check_in,
            check_out=check_out,
            adults=2,
            currency="USD"
        )

        # Print search results
        print(f"\nSearch results for hotels in New York:")

        # Print hotels
        hotels = results.get('hotels', [])
        print(f"Hotels found: {len(hotels)}")

        # Print first few hotels
        for i, hotel in enumerate(hotels[:3]):
            print(f"\nHotel {i+1}:")
            print(f"Name: {hotel.get('name')}")
            print(f"Price: {hotel.get('price')}")
            print(f"Rating: {hotel.get('rating')}")
            print(f"Location: {hotel.get('location')}")

            # If we have a hotel ID, get details
            if hotel.get('id'):
                print("\nGetting hotel details...")
                details = hotels_tool.get_hotel_details(
                    hotel_id=hotel.get('id'),
                    check_in=check_in,
                    check_out=check_out,
                    adults=2
                )
                print(f"Description: {details.get('description', '')[:100]}...")

    except Exception as e:
        print(f"Error: {e}")
