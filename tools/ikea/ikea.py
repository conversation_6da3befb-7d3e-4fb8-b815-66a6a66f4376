#!/usr/bin/env python3
"""
IKEA search module.

This module provides a simple interface to the IKEA search API.
"""

from typing import Dict, Any, Optional
from .ikea_tool import IkeaTool

# Global tool instance
_ikea_tool = None


def get_ikea_tool(zyte_api_key: Optional[str] = None) -> IkeaTool:
    """
    Get or create a global IKEA tool instance.
    
    Args:
        zyte_api_key: Zyte API key for authentication
    
    Returns:
        IkeaTool: The IKEA tool instance
    """
    global _ikea_tool
    if _ikea_tool is None:
        _ikea_tool = IkeaTool(zyte_api_key=zyte_api_key)
    return _ikea_tool


def search(query: str, zyte_api_key: Optional[str] = None, **kwargs) -> Dict[str, Any]:
    """
    Search for products on IKEA.
    
    Args:
        query (str): Search query for IKEA products
        zyte_api_key (str, optional): Zyte API key for authentication
        **kwargs: Additional search parameters
        
    Returns:
        Dict[str, Any]: Search results from IKEA
    """
    tool = get_ikea_tool(zyte_api_key)
    return tool.search(query, **kwargs)


def check_health(zyte_api_key: Optional[str] = None) -> Dict[str, Any]:
    """
    Check if the IKEA service is available.
    
    Args:
        zyte_api_key (str, optional): Zyte API key for authentication
    
    Returns:
        Dict[str, Any]: Health status
    """
    tool = get_ikea_tool(zyte_api_key)
    return tool.check_health()


def get_available_endpoints() -> Dict[str, str]:
    """
    Get available IKEA API endpoints.
    
    Returns:
        Dict[str, str]: Available endpoints
    """
    return {
        'root': 'GET /',
        'search': 'GET /search?query=<query>&zyte_api_key=<key>'
    }