"""
Image search module.

This module provides a simple interface to the Image search API.
"""

from typing import Dict, Any, Optional

from .images_tool import ImagesTool

# Create a default instance with environment variables
_default_tool = ImagesTool()

def search(query: str,
           limit: int = 10,
           lang: str = "en",
           api_key: Optional[str] = None,
           **kwargs) -> Dict[str, Any]:
    """
    Search for images.

    Args:
        query: Search query
        limit: Maximum number of results to return (1-100)
        lang: Language for search results
        api_key: SerpAPI key
        **kwargs: Additional search parameters

    Returns:
        Image search results
    """
    # Create a custom instance if API key is provided
    if api_key:
        tool = ImagesTool(api_key=api_key)
        return tool.search(query=query, limit=limit, lang=lang, **kwargs)
    
    # Otherwise use the default instance
    return _default_tool.search(query=query, limit=limit, lang=lang, **kwargs)

def check_health() -> Dict[str, Any]:
    """
    Check if the Images API is healthy.

    Returns:
        API health status
    """
    return _default_tool.check_health()


__all__ = ['search', 'check_health']