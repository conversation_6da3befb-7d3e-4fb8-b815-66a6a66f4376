#!/usr/bin/env python3
"""
Image search tool consumer.
"""

import os
from typing import Dict, Any, List, Optional, Union

try:
    # When imported as a module
    from ..base_tool import BaseTool
except ImportError:
    # When run directly
    import sys
    import os
    sys.path.append(os.path.dirname(os.path.dirname(__file__)))
    from base_tool import BaseTool


class ImagesTool(BaseTool):
    """
    Tool for searching images using SerpAPI.
    """

    def __init__(self,
                base_url: str = None,
                timeout: int = 60,
                serpapi_key: Optional[str] = None,
                env_file: str = None):
        """
        Initialize the Image search tool.

        Args:
            base_url: Base URL for the Image API
            timeout: Request timeout in seconds
            serpapi_key: SerpAPI key
            env_file: Path to .env file
        """
        # Get base URL from environment variable if not provided
        if base_url is None:
            base_url = os.environ.get("IMAGES_BASE_URL", "http://localhost:9286")
        
                # Initialize base class (this will load environment variables)
        super().__init__(base_url, timeout, env_file)

        # Get API key from parameter or environment variable
        self.api_key = serpapi_key or os.environ.get("SERPAPI_KEY", "")

    def search(self,
              query: str,
              limit: int = 10,
              lang: str = "en",
              debug: bool = False,
              **kwargs) -> Dict[str, Any]:
        """
        Search for images.

        Args:
            query: Search query
            limit: Maximum number of results to return (1-100)
            lang: Language for search results
            debug: Enable debug mode
            **kwargs: Additional search parameters

        Returns:
            Image search results
        """
        # Prepare request parameters
        params = {
            "query": query,
            "limit": limit,
            "lang": lang,
            "debug": debug
        }

        # Add API key if available
        if self.api_key:
            params["api_key"] = self.api_key

        # Add any additional parameters
        params.update(kwargs)

        # Make the request
        return self._make_request("GET", "/images/search", params=params)


# Example usage
if __name__ == "__main__":
    # Create the Image search tool
    images_tool = ImagesTool()

    try:
        # Check if the API is healthy
        health = images_tool.check_health()
        print(f"API Health: {health}")

        # Perform a search
        results = images_tool.search(
            query="mountain landscape",
            limit=3
        )

        # Print search results
        print(f"\nSearch results for 'mountain landscape':")

        # Print images
        images = results.get('images', [])
        print(f"Images found: {len(images)}")

        # Print each image
        for i, image in enumerate(images):
            print(f"\nImage {i+1}:")
            print(f"Title: {image.get('title')}")
            print(f"Source: {image.get('source')}")
            print(f"Link: {image.get('source_link')}")
            print(f"Dimensions: {image.get('dimensions')}")

        # Print shopping results
        shopping = results.get('shopping', [])
        if shopping:
            print(f"\nShopping results found: {len(shopping)}")
            for i, item in enumerate(shopping[:2]):
                print(f"\nItem {i+1}:")
                print(f"Title: {item.get('title')}")
                print(f"Price: {item.get('price')}")
                print(f"Source: {item.get('source')}")

        # Print related searches
        related = results.get('related_searches', [])
        if related:
            print(f"\nRelated searches found: {len(related)}")
            for i, search in enumerate(related[:3]):
                print(f"\nRelated search {i+1}:")
                print(f"Name: {search.get('name')}")
                print(f"Query: {search.get('query')}")

    except Exception as e:
        print(f"Error: {e}")
