from .immoweb_tool import ImmowebTool
from typing import Dict, Any, Optional


def search(query: str, zyte_api_key: Optional[str] = None, **kwargs) -> Dict[str, Any]:
    """
    Search for properties on Immoweb.
    
    Args:
        query (str): Search query for Immoweb properties
        zyte_api_key (Optional[str]): Zyte API key for authentication
        **kwargs: Additional parameters
        
    Returns:
        Dict[str, Any]: Search results from Immoweb
    """
    tool = ImmowebTool(zyte_api_key=zyte_api_key)
    return tool.search(query, **kwargs)


def check_health() -> Dict[str, Any]:
    """
    Check if the Immoweb service is available.
    
    Returns:
        Dict[str, Any]: Health status of Immoweb service
    """
    tool = ImmowebTool()
    return tool.check_health()


def get_available_features() -> Dict[str, Any]:
    """
    Get available features for Immoweb tool.
    
    Returns:
        Dict[str, Any]: Available features
    """
    return