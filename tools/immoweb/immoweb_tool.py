import os
import requests
from typing import Dict, Any, Optional
from ..base_tool import BaseTool


class ImmowebTool(BaseTool):
    """
    Tool for searching Immoweb properties using Zyte API.
    """
    
    def __init__(self, zyte_api_key: Optional[str] = None):
        super().__init__("immoweb")
        self.base_url = "http://localhost:9294"
        self.zyte_api_key = zyte_api_key or os.getenv('ZYTE_API_KEY')
    
    def search(self, query: str, **kwargs) -> Dict[str, Any]:
        """
        Search for properties on Immoweb.
        
        Args:
            query (str): Search query for Immoweb properties
            **kwargs: Additional parameters
            
        Returns:
            Dict[str, Any]: Search results from Immoweb
        """
        try:
            params = {
                'query': query,
                'limit': 3  # Limit to 3 properties for faster response
            }

            if self.zyte_api_key:
                params['zyte_api_key'] = self.zyte_api_key

            response = self.make_request(
                method='GET',
                url=f"{self.base_url}/search",
                params=params,
                timeout=120  # Increase timeout to 2 minutes
            )
            
            return response
            
        except Exception as e:
            return {
                'error': f"Immoweb search failed: {str(e)}",
                'query': query
            }
    
    def check_health(self) -> Dict[str, Any]:
        """
        Check if the Immoweb service is available.
        
        Returns:
            Dict[str, Any]: Health status
        """
        try:
            response = self.make_request(
                method='GET',
                url=self.base_url
            )
            return {
                'status': 'healthy',
                'service': 'immoweb',
                'response': response
            }
        except Exception as e:
            return {
                'status': 'unhealthy',
                'service': 'immoweb',
                'error': str(e)
            }


# Example usage
if __name__ == "__main__":
    tool = ImmowebTool()
    result = tool.search("apartment Brussels")
    print(result)