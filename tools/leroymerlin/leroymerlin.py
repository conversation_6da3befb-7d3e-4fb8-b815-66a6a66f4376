from .leroymerlin_tool import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from typing import Dict, Any, Optional


def search(query: str, zyte_api_key: Optional[str] = None, **kwargs) -> Dict[str, Any]:
    """
    Search for products on Leroy Merlin.
    
    Args:
        query (str): Search query for Leroy Merlin products
        zyte_api_key (Optional[str]): Zyte API key for authentication
        **kwargs: Additional parameters
        
    Returns:
        Dict[str, Any]: Search results from <PERSON>
    """
    tool = LeroyMerlinTool(zyte_api_key=zyte_api_key)
    return tool.search(query, **kwargs)


def check_health() -> Dict[str, Any]:
    """
    Check if the Leroy Merlin service is available.
    
    Returns:
        Dict[str, Any]: Health status of Leroy Merlin service
    """
    tool = LeroyMerlinTool()
    return tool.check_health()


def get_available_features() -> Dict[str, Any]:
    """
    Get available features for Leroy Merlin tool.
    
    Returns:
        Dict[str, Any]: Available features
    """
    return {
        'search': 'Search for products on Leroy Merlin',
        'health_check': 'Check service availability',
        'api_type': 'Zyte API',
        'method': 'GET',
        'port': 9310
    }