from .lidl_tool import LidlTool
from typing import Dict, Any, Optional


def search(query: str, zyte_api_key: Optional[str] = None, **kwargs) -> Dict[str, Any]:
    """
    Search for products on Lidl.
    
    Args:
        query (str): Search query for Lidl products
        zyte_api_key (Optional[str]): Zyte API key for authentication
        **kwargs: Additional parameters
        
    Returns:
        Dict[str, Any]: Search results from Lidl
    """
    tool = LidlTool(zyte_api_key=zyte_api_key)
    return tool.search(query, **kwargs)


def check_health() -> Dict[str, Any]:
    """
    Check if the Lidl service is available.
    
    Returns:
        Dict[str, Any]: Health status of Lidl service
    """
    tool = LidlTool()
    return tool.check_health()


def get_available_features() -> Dict[str, Any]:
    """
    Get available features for Lidl tool.
    
    Returns:
        Dict[str, Any]: Available features
    """
    return {
        'search': 'Search for products on Lidl',
        'health_check': 'Check service availability',
        'api_type': 'Zyte API',
        'method': 'GET',
        'port': 9295
    }