"""
Deepseek LLM provider implementation.
"""
import os
import logging
from typing import List, Dict, Any, Optional, AsyncGenerator
from openai import AsyncOpenAI
from llm.base import LLMProvider


class DeepseekProvider(LLMProvider):
    """
    Deepseek API provider implementation.

    Handles communication with Deepseek's API for text generation and embeddings.
    Uses the OpenAI client with a custom base URL.
    """

    # Models that require special handling (thinking models)
    THINKING_MODELS = ["deepseek-reasoner"]

    def __init__(self, api_key: Optional[str] = None):
        """
        Initialize the Deepseek provider.

        Args:
            api_key: Deepseek API key (defaults to DEEPSEEK_API_KEY environment variable)
        """
        self.api_key = api_key or os.getenv("DEEPSEEK_API_KEY")
        if not self.api_key:
            raise ValueError("Deepseek API key is required")

        self.client = AsyncOpenAI(
            api_key=self.api_key,
            base_url="https://api.deepseek.com"
        )

    def _is_thinking_model(self, model: str) -> bool:
        """Check if the model is a thinking model."""
        return model.lower() in [m.lower() for m in self.THINKING_MODELS]

    async def generate(
        self,
        messages: List[Dict[str, str]],
        model: str = "deepseek-chat",
        temperature: Optional[float] = 0.0,
        max_tokens: Optional[int] = None,
        max_completion_tokens: Optional[int] = None,
        reasoning_effort: Optional[str] = None,
        **kwargs
    ) -> str:
        """
        Generate a response using Deepseek's API.

        Args:
            messages: List of message dictionaries with 'role' and 'content' keys
            model: Deepseek model identifier (default: deepseek-chat)
            temperature: Controls randomness (0.0 to 1.0), not used for thinking models
            max_tokens: Maximum number of tokens to generate (for non-thinking models)
            max_completion_tokens: Maximum number of tokens to generate (for thinking models)
            reasoning_effort: Reasoning effort for thinking models (low, medium, high)
            **kwargs: Additional API parameters

        Returns:
            Generated text response
        """
        # Validate model name
        if model.lower() not in ["deepseek-chat", "deepseek-reasoner"]:
            raise ValueError(f"Unsupported Deepseek model: {model}")

        params = {
            "model": model,
            "messages": messages,
        }

        is_thinking = self._is_thinking_model(model)

        # For thinking models (deepseek-reasoner)
        if is_thinking:
            logging.info(f"Using thinking model: {model}")

            # Handle reasoning_effort parameter if Deepseek supports it
            if reasoning_effort:
                params["reasoning_effort"] = reasoning_effort
                logging.info(f"Setting reasoning_effort={reasoning_effort} for model {model}")

            # Handle token limit for thinking models
            if max_completion_tokens is not None:
                logging.info(f"Using max_completion_tokens={max_completion_tokens} for model {model}")
                params["max_completion_tokens"] = max_completion_tokens
            elif max_tokens is not None:
                logging.info(f"Using max_tokens={max_tokens} as max_completion_tokens for model {model}")
                params["max_completion_tokens"] = max_tokens
        else:
            # For standard models (deepseek-chat)
            # Set temperature
            if temperature is not None:
                params["temperature"] = temperature

            # Handle token limit for regular models
            if max_tokens is not None:
                logging.info(f"Using max_tokens={max_tokens} for model {model}")
                params["max_tokens"] = max_tokens

        # Add any additional parameters
        params.update(kwargs)

        response = await self.client.chat.completions.create(**params)
        return response.choices[0].message.content

    async def stream_generate(
        self,
        messages: List[Dict[str, str]],
        model: str = "deepseek-chat",
        temperature: Optional[float] = 0.0,
        max_tokens: Optional[int] = None,
        max_completion_tokens: Optional[int] = None,
        reasoning_effort: Optional[str] = None,
        **kwargs
    ) -> AsyncGenerator[str, None]:
        """
        Stream a response using Deepseek's API.

        Args:
            messages: List of message dictionaries with 'role' and 'content' keys
            model: Deepseek model identifier (default: deepseek-chat)
            temperature: Controls randomness (0.0 to 1.0), not used for thinking models
            max_tokens: Maximum number of tokens to generate (for non-thinking models)
            max_completion_tokens: Maximum number of tokens to generate (for thinking models)
            reasoning_effort: Reasoning effort for thinking models (low, medium, high)
            **kwargs: Additional API parameters

        Returns:
            AsyncGenerator yielding chunks of the response as they become available
        """
        # Validate model name
        if model.lower() not in ["deepseek-chat", "deepseek-reasoner"]:
            raise ValueError(f"Unsupported Deepseek model: {model}")

        logging.info(f"Starting Deepseek streaming with model: {model}")

        params = {
            "model": model,
            "messages": messages,
            "stream": True,
        }

        is_thinking = self._is_thinking_model(model)

        # For thinking models (deepseek-reasoner)
        if is_thinking:
            logging.info(f"Using thinking model with streaming: {model}")

            # Handle reasoning_effort parameter if Deepseek supports it
            if reasoning_effort:
                params["reasoning_effort"] = reasoning_effort
                logging.info(f"Setting reasoning_effort={reasoning_effort} for model {model}")

            # Handle token limit for thinking models
            if max_completion_tokens is not None:
                logging.info(f"Using max_completion_tokens={max_completion_tokens} for model {model}")
                params["max_completion_tokens"] = max_completion_tokens
            elif max_tokens is not None:
                logging.info(f"Using max_tokens={max_tokens} as max_completion_tokens for model {model}")
                params["max_completion_tokens"] = max_tokens
        else:
            # For standard models (deepseek-chat)
            # Set temperature
            if temperature is not None:
                params["temperature"] = temperature

            # Handle token limit for regular models
            if max_tokens is not None:
                logging.info(f"Using max_tokens={max_tokens} for model {model}")
                params["max_tokens"] = max_tokens

        # Add any additional parameters
        params.update(kwargs)

        chunk_count = 0
        total_tokens = 0

        stream = await self.client.chat.completions.create(**params)

        async for chunk in stream:
            if chunk.choices and chunk.choices[0].delta.content:
                chunk_content = chunk.choices[0].delta.content
                chunk_count += 1
                total_tokens += len(chunk_content)
                if chunk_count % 10 == 0:
                    logging.debug(f"Deepseek streaming: received {chunk_count} chunks, {total_tokens} chars so far")
                yield chunk_content

        logging.info(f"Deepseek streaming complete: {chunk_count} chunks, {total_tokens} chars total")

    async def embed(self, text: str, model: str = "deepseek-embedding") -> List[float]:
        """
        Generate embeddings using Deepseek's API.

        Args:
            text: The text to embed
            model: Deepseek embedding model to use

        Returns:
            List of floating point values representing the embedding
        """
        response = await self.client.embeddings.create(
            model=model,
            input=text
        )
        return response.data[0].embedding