"""
OpenAI LLM provider implementation.
"""
import os
import logging
from typing import List, Dict, Any, Optional, AsyncGenerator
from openai import AsyncOpenAI
from llm.base import LL<PERSON>rovider


class OpenAIProvider(LLMProvider):
    """
    OpenAI API provider implementation.

    Handles communication with OpenAI's API for text generation and embeddings.
    """

    # Models that require special handling (thinking models)
    THINKING_MODELS = ["o3-mini", "o1-mini", "o1"]

    def __init__(self, api_key: Optional[str] = None):
        """
        Initialize the OpenAI provider.

        Args:
            api_key: OpenAI API key (defaults to OPENAI_API_KEY environment variable)
        """
        self.api_key = api_key or os.getenv("OPENAI_API_KEY")
        if not self.api_key:
            raise ValueError("OpenAI API key is required")

        self.client = AsyncOpenAI(api_key=self.api_key)

    def _is_thinking_model(self, model: str) -> bool:
        """Check if the model is a thinking model."""
        return model in self.THINKING_MODELS

    async def generate(
        self,
        messages: List[Dict[str, str]],
        model: str = "gpt-4o",
        temperature: Optional[float] = 0.0,
        max_tokens: Optional[int] = None,
        max_completion_tokens: Optional[int] = None,
        reasoning_effort: Optional[str] = None,
        **kwargs
    ) -> str:
        """
        Generate a response using OpenAI's API.

        Args:
            messages: List of message dictionaries with 'role' and 'content' keys
            model: OpenAI model identifier (default: gpt-4o)
            temperature: Controls randomness (0.0 to 1.0), not used for thinking models
            max_tokens: Maximum number of tokens to generate (for non-thinking models)
            max_completion_tokens: Maximum number of tokens to generate (for thinking models)
            reasoning_effort: Reasoning effort for thinking models (low, medium, high)
            **kwargs: Additional OpenAI API parameters

        Returns:
            Generated text response
        """
        params = {
            "model": model,
            "messages": messages,
        }

        is_thinking = self._is_thinking_model(model)

        # For thinking models (o1, o3, etc.)
        if is_thinking:
            logging.info(f"Using thinking model: {model}")

            # Handle reasoning_effort parameter
            if reasoning_effort:
                params["reasoning_effort"] = reasoning_effort
                logging.info(f"Setting reasoning_effort={reasoning_effort} for model {model}")

            # Handle token limit for thinking models
            if max_completion_tokens is not None:
                logging.info(f"Using max_completion_tokens={max_completion_tokens} for model {model}")
                params["max_completion_tokens"] = max_completion_tokens
            elif max_tokens is not None:
                logging.info(f"Using max_tokens={max_tokens} as max_completion_tokens for model {model}")
                params["max_completion_tokens"] = max_tokens
        else:
            # For standard models (gpt-4o, etc.)
            # Set temperature
            if temperature is not None:
                params["temperature"] = temperature

            # Handle token limit for regular models
            if max_tokens is not None:
                logging.info(f"Using max_tokens={max_tokens} for model {model}")
                params["max_tokens"] = max_tokens

        # Add any additional parameters
        params.update(kwargs)

        response = await self.client.chat.completions.create(**params)
        return response.choices[0].message.content

    async def stream_generate(
        self,
        messages: List[Dict[str, str]],
        model: str = "gpt-4o",
        temperature: Optional[float] = 0.0,
        max_tokens: Optional[int] = None,
        max_completion_tokens: Optional[int] = None,
        reasoning_effort: Optional[str] = None,
        **kwargs
    ) -> AsyncGenerator[str, None]:
        """
        Stream a response using OpenAI's API.

        Args:
            messages: List of message dictionaries with 'role' and 'content' keys
            model: OpenAI model identifier (default: gpt-4o)
            temperature: Controls randomness (0.0 to 1.0), not used for thinking models
            max_tokens: Maximum number of tokens to generate (for non-thinking models)
            max_completion_tokens: Maximum number of tokens to generate (for thinking models)
            reasoning_effort: Reasoning effort for thinking models (low, medium, high)
            **kwargs: Additional OpenAI API parameters

        Returns:
            AsyncGenerator yielding chunks of the response as they become available
        """
        # logging.info(f"Starting OpenAI streaming with model: {model}")

        params = {
            "model": model,
            "messages": messages,
            "stream": True,
        }

        is_thinking = self._is_thinking_model(model)

        # For thinking models (o1, o3, etc.)
        if is_thinking:
            logging.info(f"Using thinking model with streaming: {model}")

            # Handle reasoning_effort parameter
            if reasoning_effort:
                params["reasoning_effort"] = reasoning_effort
                logging.info(f"Setting reasoning_effort={reasoning_effort} for model {model}")

            # Handle token limit for thinking models
            if max_completion_tokens is not None:
                logging.info(f"Using max_completion_tokens={max_completion_tokens} for model {model}")
                params["max_completion_tokens"] = max_completion_tokens
            elif max_tokens is not None:
                logging.info(f"Using max_tokens={max_tokens} as max_completion_tokens for model {model}")
                params["max_completion_tokens"] = max_tokens
        else:
            # For standard models (gpt-4o, etc.)
            # Set temperature
            if temperature is not None:
                params["temperature"] = temperature

            # Handle token limit for regular models
            if max_tokens is not None:
                logging.info(f"Using max_tokens={max_tokens} for model {model}")
                params["max_tokens"] = max_tokens

        # Add any additional parameters
        params.update(kwargs)

        chunk_count = 0
        total_tokens = 0

        stream = await self.client.chat.completions.create(**params)

        async for chunk in stream:
            if chunk.choices and chunk.choices[0].delta.content:
                chunk_content = chunk.choices[0].delta.content
                chunk_count += 1
                total_tokens += len(chunk_content)
                if chunk_count % 10 == 0:
                    logging.debug(f"OpenAI streaming: received {chunk_count} chunks, {total_tokens} chars so far")
                yield chunk_content

        logging.info(f"OpenAI streaming complete: {chunk_count} chunks, {total_tokens} chars total")

    async def embed(self, text: str, model: str = "text-embedding-3-small") -> List[float]:
        """
        Generate embeddings using OpenAI's API.

        Args:
            text: The text to embed
            model: OpenAI embedding model to use

        Returns:
            List of floating point values representing the embedding
        """
        response = await self.client.embeddings.create(
            model=model,
            input=text
        )
        return response.data[0].embedding