"""
OpenRouter LLM provider implementation.
"""
import os
import logging
from typing import List, Dict, Any, Optional, AsyncGenerator
from openai import Async<PERSON>penAI
from llm.base import LLMProvider


class OpenRouterProvider(LLMProvider):
    """
    OpenRouter API provider implementation.

    Handles communication with OpenRouter's API for accessing multiple LLM providers
    through a unified interface compatible with OpenAI's API.
    """

    # Base URL for OpenRouter API
    BASE_URL = "https://openrouter.ai/api/v1"

    # Default models - these are the model IDs used in OpenRouter
    DEFAULT_MODEL = "openai/gpt-4o"
    FAST_MODEL = "anthropic/claude-3-haiku"
    POWERFUL_MODEL = "anthropic/claude-3-opus"
    EMBEDDING_MODEL = "openai/text-embedding-3-large"

    def __init__(
        self,
        api_key: Optional[str] = None,
        site_url: Optional[str] = None,
        site_name: Optional[str] = None
    ):
        """
        Initialize the OpenRouter provider.

        Args:
            api_key: OpenRouter API key (defaults to OPENROUTER_API_KEY environment variable)
            site_url: Your site URL for rankings on openrouter.ai
            site_name: Your site name for rankings on openrouter.ai
        """
        self.api_key = api_key or os.getenv("OPENROUTER_API_KEY")
        if not self.api_key:
            raise ValueError("OpenRouter API key is required")

        # Store site information for headers
        self.site_url = site_url or os.getenv("OPENROUTER_SITE_URL", "")
        self.site_name = site_name or os.getenv("OPENROUTER_SITE_NAME", "")

        # Initialize AsyncOpenAI client with OpenRouter base URL
        self.client = AsyncOpenAI(
            base_url=self.BASE_URL,
            api_key=self.api_key
        )

        logging.info("OpenRouter provider initialized")

    def _get_headers(self) -> Dict[str, str]:
        """
        Get the extra headers required for OpenRouter API calls.

        Returns:
            Dictionary of HTTP headers
        """
        headers = {}

        # Add site URL if available
        if self.site_url:
            headers["HTTP-Referer"] = self.site_url

        # Add site name if available
        if self.site_name:
            headers["X-Title"] = self.site_name

        return headers

    def _clean_params(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """
        Clean parameters to ensure compatibility with OpenRouter API.

        Args:
            params: Dictionary of parameters

        Returns:
            Cleaned parameters dictionary
        """
        # Make a copy to avoid modifying the original
        cleaned = params.copy()

        # Remove parameters that aren't supported by OpenRouter API
        if 'provider' in cleaned:
            # If provider is specified, use route parameter instead
            provider_info = cleaned.pop('provider')
            # Only set route if it's not already set
            if 'route' not in cleaned and isinstance(provider_info, dict):
                cleaned['route'] = provider_info

        return cleaned

    async def generate(
        self,
        messages: List[Dict[str, str]],
        model: str = DEFAULT_MODEL,
        temperature: Optional[float] = 0.0,
        max_tokens: Optional[int] = None,
        max_completion_tokens: Optional[int] = None,
        reasoning_effort: Optional[str] = None,
        **kwargs
    ) -> str:
        """
        Generate a response using OpenRouter's API.

        Args:
            messages: List of message dictionaries with 'role' and 'content' keys
            model: OpenRouter model identifier (default: openai/gpt-4o)
            temperature: Controls randomness (0.0 to 1.0)
            max_tokens: Maximum number of tokens to generate
            max_completion_tokens: Not used by OpenRouter, included for compatibility
            reasoning_effort: Not used by OpenRouter, included for compatibility
            **kwargs: Additional API parameters

        Returns:
            Generated text response
        """
        # Prepare request parameters
        params = {
            "model": model,
            "messages": messages,
            "extra_headers": self._get_headers()
        }



        # Add temperature if provided
        if temperature is not None:
            params["temperature"] = temperature

        # Add max tokens if provided
        if max_tokens is not None:
            params["max_tokens"] = max_tokens
        elif max_completion_tokens is not None:
            # Use max_completion_tokens as fallback
            params["max_tokens"] = max_completion_tokens

        # Add any additional parameters and clean them
        params.update(kwargs)
        params = self._clean_params(params)

        try:
            # Make the API call
            response = await self.client.chat.completions.create(**params)

            # Extract and return the response content
            if response.choices and len(response.choices) > 0:
                return response.choices[0].message.content or ""
            else:
                logging.warning("Empty response from OpenRouter API")
                return ""
        except Exception as e:
            logging.error(f"Error generating content with OpenRouter: {str(e)}")
            raise

    async def stream_generate(
        self,
        messages: List[Dict[str, str]],
        model: str = DEFAULT_MODEL,
        temperature: Optional[float] = 0.0,
        max_tokens: Optional[int] = None,
        max_completion_tokens: Optional[int] = None,
        reasoning_effort: Optional[str] = None,
        **kwargs
    ) -> AsyncGenerator[str, None]:
        """
        Stream a response using OpenRouter's API.

        Args:
            messages: List of message dictionaries with 'role' and 'content' keys
            model: OpenRouter model identifier (default: openai/gpt-4o)
            temperature: Controls randomness (0.0 to 1.0)
            max_tokens: Maximum number of tokens to generate
            max_completion_tokens: Not used by OpenRouter, included for compatibility
            reasoning_effort: Not used by OpenRouter, included for compatibility
            **kwargs: Additional API parameters

        Returns:
            AsyncGenerator yielding chunks of the response as they become available
        """
        logging.info(f"Starting OpenRouter streaming with model: {model}")

        # Prepare request parameters
        params = {
            "model": model,
            "messages": messages,
            "stream": True,
            "extra_headers": self._get_headers()
        }

        if temperature is not None:
            params["temperature"] = temperature

        # Add max tokens if provided
        if max_tokens is not None:
            params["max_tokens"] = max_tokens
        elif max_completion_tokens is not None:
            # Use max_completion_tokens as fallback
            params["max_tokens"] = max_completion_tokens

        # Add any additional parameters and clean them
        params.update(kwargs)
        params = self._clean_params(params)

        try:
            # Stream the response
            chunk_count = 0
            total_tokens = 0

            stream = await self.client.chat.completions.create(**params)

            async for chunk in stream:
                if chunk.choices and len(chunk.choices) > 0 and chunk.choices[0].delta.content:
                    chunk_content = chunk.choices[0].delta.content
                    chunk_count += 1
                    total_tokens += len(chunk_content)
                    if chunk_count % 10 == 0:
                        logging.debug(f"OpenRouter streaming: received {chunk_count} chunks, {total_tokens} chars so far")
                    yield chunk_content

            logging.info(f"OpenRouter streaming complete: {chunk_count} chunks, {total_tokens} chars total")
        except Exception as e:
            logging.error(f"Error streaming content with OpenRouter: {str(e)}")
            raise

    async def embed(self, text: str, model: str = EMBEDDING_MODEL) -> List[float]:
        """
        Generate embeddings using OpenRouter's API.

        Args:
            text: The text to embed
            model: OpenRouter embedding model to use

        Returns:
            List of floating point values representing the embedding
        """
        logging.info(f"Generating embeddings with OpenRouter model: {model}")

        try:
            # OpenRouter uses the same embedding API format as OpenAI
            params = {
                "model": model,
                "input": text,
                "extra_headers": self._get_headers()
            }

            # Clean parameters
            params = self._clean_params(params)

            response = await self.client.embeddings.create(**params)

            # Extract and return the embedding
            if response.data and len(response.data) > 0:
                return response.data[0].embedding
            else:
                logging.warning("Empty embedding response from OpenRouter API")
                return []
        except Exception as e:
            logging.error(f"Error generating embeddings with OpenRouter: {str(e)}")
            raise