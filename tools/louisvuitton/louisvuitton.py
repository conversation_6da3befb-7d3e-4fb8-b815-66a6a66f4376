from .louisvuitton_tool import Louis<PERSON><PERSON>tonTool
from typing import Dict, Any, Optional


def search(query: str, zyte_api_key: Optional[str] = None, **kwargs) -> Dict[str, Any]:
    """
    Search for products on Louis Vuitton.
    
    Args:
        query (str): Search query for Louis Vuitton products
        zyte_api_key (Optional[str]): Zyte API key for authentication
        **kwargs: Additional parameters
        
    Returns:
        Dict[str, Any]: Search results from Louis Vuitton
    """
    tool = LouisVuittonTool(zyte_api_key=zyte_api_key)
    return tool.search(query, **kwargs)


def check_health() -> Dict[str, Any]:
    """
    Check if the Louis Vuitton service is available.
    
    Returns:
        Dict[str, Any]: Health status of Louis Vuitton service
    """
    tool = LouisVuittonTool()
    return tool.check_health()


def get_available_features() -> Dict[str, Any]:
    """
    Get available features for Louis Vuitton tool.
    
    Returns:
        Dict[str, Any]: Available features
    """
    return {
        'search': 'Search for products on Louis Vuitton',
        'health_check': 'Check service availability',
        'api_type': 'Zyte API',
        'method': 'GET',
        'port': 9311
    }