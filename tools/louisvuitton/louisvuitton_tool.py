#!/usr/bin/env python3
"""
Louis Vuitton search tool consumer.
"""

import os
from typing import Dict, Any, Optional

try:
    # When imported as a module
    from ..base_tool import BaseTool
except ImportError:
    # When run directly
    import sys
    import os
    sys.path.append(os.path.dirname(os.path.dirname(__file__)))
    from base_tool import BaseTool


class LouisVuittonTool(BaseTool):
    """
    Tool for searching Louis Vuitton products using Zyte API.
    """

    def __init__(self,
                zyte_api_key: Optional[str] = None,
                base_url: str = None,
                timeout: int = 60,
                env_file: str = None):
        """
        Initialize the Louis Vuitton search tool.

        Args:
            zyte_api_key: Zyte API key for authentication
            base_url: Base URL for the Louis Vuitton API
            timeout: Request timeout in seconds
            env_file: Path to .env file
        """
        # Get base URL from environment variable if not provided
        if base_url is None:
            base_url = os.environ.get("LOUISVUITTON_BASE_URL", "http://localhost:9311")
        
                # Initialize base class (this will load environment variables)
        super().__init__(base_url, timeout, env_file)
        self.zyte_api_key = zyte_api_key or os.getenv('ZYTE_API_KEY')

    def search(self, query: str, **kwargs) -> Dict[str, Any]:
        """
        Search for products on Louis Vuitton.
        
        Args:
            query (str): Search query for Louis Vuitton products
            **kwargs: Additional parameters
            
        Returns:
            Dict[str, Any]: Search results from Louis Vuitton
        """
        try:
            params = {
                'query': query,
                'countries': 'United States',  # Default to US for faster response
                'limit': 5  # Limit to 5 products for faster response
            }

            if self.zyte_api_key:
                params['zyte_api_key'] = self.zyte_api_key

            # Add any additional parameters
            params.update(kwargs)

            response = self._make_request(
                method="GET",
                endpoint="/search",
                params=params,
                timeout=120  # Increase timeout to 2 minutes
            )
            
            return response
            
        except Exception as e:
            return {
                'error': f"Louis Vuitton search failed: {str(e)}",
                'query': query
            }
    
    def check_health(self) -> Dict[str, Any]:
        """
        Check if the Louis Vuitton service is available.
        
        Returns:
            Dict[str, Any]: Health status
        """
        try:
            response = self._make_request(
                method="GET",
                endpoint="/"
            )
            return {
                'status': 'healthy',
                'service': 'louisvuitton',
                'response': response
            }
        except Exception as e:
            return {
                'status': 'unhealthy',
                'service': 'louisvuitton',
                'error': str(e)
            }


# Example usage
if __name__ == "__main__":
    tool = LouisVuittonTool()
    
    try:
        # Check if the API is healthy
        health = tool.check_health()
        print(f"API Health: {health}")
        
        # Perform a search
        result = tool.search("bag")
        print(f"\nSearch results for 'bag':")
        print(result)
        
    except Exception as e:
        print(f"Error: {e}")