#!/usr/bin/env python3
"""
ManoMano search module.

This module provides a simple interface to the ManoMano search API.
"""

from typing import Dict, Any, Optional
from .manomano_tool import ManoManoTool

# Global tool instance
_manomano_tool = None


def get_manomano_tool(zyte_api_key: Optional[str] = None) -> ManoManoTool:
    """
    Get or create a global ManoMano tool instance.
    
    Args:
        zyte_api_key: Zyte API key for authentication
    
    Returns:
        ManoManoTool: The ManoMano tool instance
    """
    global _manomano_tool
    if _manomano_tool is None:
        _manomano_tool = ManoManoTool(zyte_api_key=zyte_api_key)
    return _manomano_tool


def search(query: str, zyte_api_key: Optional[str] = None, **kwargs) -> Dict[str, Any]:
    """
    Search for products on ManoMano.
    
    Args:
        query (str): Search query for ManoMano products
        zyte_api_key (str, optional): Zyte API key for authentication
        **kwargs: Additional search parameters
        
    Returns:
        Dict[str, Any]: Search results from ManoMano
    """
    tool = get_manomano_tool(zyte_api_key)
    return tool.search(query, **kwargs)


def check_health(zyte_api_key: Optional[str] = None) -> Dict[str, Any]:
    """
    Check if the ManoMano service is available.
    
    Args:
        zyte_api_key (str, optional): Zyte API key for authentication
    
    Returns:
        Dict[str, Any]: Health status
    """
    tool = get_manomano_tool(zyte_api_key)
    return tool.check_health()


def get_available_endpoints() -> Dict[str, str]:
    """
    Get available ManoMano API endpoints.
    
    Returns:
        Dict[str, str]: Available endpoints
    """
    return {
        'root': 'GET /',
        'search': 'GET /search?query=<query>&zyte_api_key=<key>'
    }