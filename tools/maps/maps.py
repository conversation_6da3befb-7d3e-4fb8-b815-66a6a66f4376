"""
Maps search module.

This module provides a simple interface to the Maps API for searching
places and getting directions.
"""

from typing import Dict, Any, Optional, List

from .maps_tool import MapsTool

# Create a default instance with environment variables
_default_tool = MapsTool()

def search_places(query: str,
                 lat: Optional[float] = None,
                 lng: Optional[float] = None,
                 radius: int = 5000,
                 place_type: Optional[str] = None,
                 min_price: Optional[int] = None,
                 max_price: Optional[int] = None,
                 open_now: Optional[bool] = None,
                 api_key: Optional[str] = None,
                 **kwargs) -> Dict[str, Any]:
    """
    Search for places.

    Args:
        query: Search query (e.g., 'restaurants in San Francisco')
        lat: Latitude for the search location
        lng: Longitude for the search location
        radius: Search radius in meters (max 50000)
        place_type: Type of place to search for
        min_price: Minimum price level (0-4)
        max_price: Maximum price level (0-4)
        open_now: Return only places that are open now
        api_key: Google Maps API key
        **kwargs: Additional search parameters

    Returns:
        Places search results
    """
    # Create a custom instance if API key is provided
    if api_key:
        tool = MapsTool(api_key=api_key)
        return tool.search_places(
            query=query,
            lat=lat,
            lng=lng,
            radius=radius,
            place_type=place_type,
            min_price=min_price,
            max_price=max_price,
            open_now=open_now,
            **kwargs
        )
    
    # Otherwise use the default instance
    return _default_tool.search_places(
        query=query,
        lat=lat,
        lng=lng,
        radius=radius,
        place_type=place_type,
        min_price=min_price,
        max_price=max_price,
        open_now=open_now,
        **kwargs
    )

def get_place_details(place_id: str,
                     fields: Optional[List[str]] = None,
                     api_key: Optional[str] = None,
                     **kwargs) -> Dict[str, Any]:
    """
    Get details for a specific place.

    Args:
        place_id: The unique identifier for a place
        fields: List of fields to include
        api_key: Google Maps API key
        **kwargs: Additional parameters

    Returns:
        Place details
    """
    # Create a custom instance if API key is provided
    if api_key:
        tool = MapsTool(api_key=api_key)
        return tool.get_place_details(place_id=place_id, fields=fields, **kwargs)
    
    # Otherwise use the default instance
    return _default_tool.get_place_details(place_id=place_id, fields=fields, **kwargs)

def get_directions(origin: str,
                  destination: str,
                  mode: str = "driving",
                  waypoints: Optional[List[str]] = None,
                  alternatives: bool = False,
                  avoid: Optional[str] = None,
                  api_key: Optional[str] = None,
                  **kwargs) -> Dict[str, Any]:
    """
    Get directions between two locations.

    Args:
        origin: Starting point (address or 'lat,lng')
        destination: End point (address or 'lat,lng')
        mode: Transportation mode (driving, walking, bicycling, transit)
        waypoints: List of waypoints
        alternatives: Whether to return alternative routes
        avoid: Features to avoid (tolls, highways, ferries)
        api_key: Google Maps API key
        **kwargs: Additional parameters

    Returns:
        Directions
    """
    # Create a custom instance if API key is provided
    if api_key:
        tool = MapsTool(api_key=api_key)
        return tool.get_directions(
            origin=origin,
            destination=destination,
            mode=mode,
            waypoints=waypoints,
            alternatives=alternatives,
            avoid=avoid,
            **kwargs
        )
    
    # Otherwise use the default instance
    return _default_tool.get_directions(
        origin=origin,
        destination=destination,
        mode=mode,
        waypoints=waypoints,
        alternatives=alternatives,
        avoid=avoid,
        **kwargs
    )

def get_place_types() -> Dict[str, Any]:
    """
    Get a list of all available place types.

    Returns:
        Dictionary of place types
    """
    return _default_tool.get_place_types()

def check_health() -> Dict[str, Any]:
    """
    Check if the Maps API is healthy.

    Returns:
        API health status
    """
    return _default_tool.check_health()


__all__ = ['search_places', 'get_place_details', 'get_directions', 'get_place_types', 'check_health']