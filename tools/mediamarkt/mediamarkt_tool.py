#!/usr/bin/env python3
"""
MediaMarkt search tool consumer.
"""

import os
from typing import Dict, Any, Optional

try:
    # When imported as a module
    from ..base_tool import BaseTool
except ImportError:
    # When run directly
    import sys
    import os
    sys.path.append(os.path.dirname(os.path.dirname(__file__)))
    from base_tool import BaseTool


class MediaMarktTool(BaseTool):
    """
    Tool for searching MediaMarkt products using Zyte API.
    """

    def __init__(self,
                zyte_api_key: Optional[str] = None,
                base_url: str = "http://localhost:9308",
                timeout: int = 300,
                env_file: str = None):
        """
        Initialize the MediaMarkt search tool.

        Args:
            zyte_api_key: Zyte API key for authentication
            base_url: Base URL for the MediaMarkt API
            timeout: Request timeout in seconds
            env_file: Path to .env file
        """
        # Initialize base class (this will load environment variables)
        super().__init__(base_url, timeout, env_file)
        self.zyte_api_key = zyte_api_key or os.getenv('ZYTE_API_KEY')

    def search(self, query: str, **kwargs) -> Dict[str, Any]:
        """
        Search for products on MediaMarkt.
        
        Args:
            query (str): Search query for MediaMarkt products
            **kwargs: Additional parameters
            
        Returns:
            Dict[str, Any]: Search results from MediaMarkt
        """
        try:
            params = {
                'query': query,
                'countries': 'Germany',  # Default to Germany for faster response
                'limit': 5  # Limit to 5 products for faster response
            }

            if self.zyte_api_key:
                params['zyte_api_key'] = self.zyte_api_key

            # Add any additional parameters
            params.update(kwargs)

            response = self._make_request(
                method="GET",
                endpoint="/search",
                params=params,
                timeout=120  # Increase timeout to 2 minutes
            )
            
            return response
            
        except Exception as e:
            return {
                'error': f"MediaMarkt search failed: {str(e)}",
                'query': query
            }
    
    def check_health(self) -> Dict[str, Any]:
        """
        Check if the MediaMarkt service is available.
        
        Returns:
            Dict[str, Any]: Health status
        """
        try:
            response = self._make_request(
                method="GET",
                endpoint="/"
            )
            return {
                'status': 'healthy',
                'service': 'mediamarkt',
                'response': response
            }
        except Exception as e:
            return {
                'status': 'unhealthy',
                'service': 'mediamarkt',
                'error': str(e)
            }


# Example usage
if __name__ == "__main__":
    tool = MediaMarktTool()
    
    try:
        # Check if the API is healthy
        health = tool.check_health()
        print(f"API Health: {health}")
        
        # Perform a search
        result = tool.search("phone")
        print(f"\nSearch results for 'phone':")
        print(result)
        
    except Exception as e:
        print(f"Error: {e}")