"""
Rank Data module.

This module provides a simple interface to the Rank Data tool.
The tool first identifies items from the input_data (used as context) based on a user prompt,
and then ranks these identified items based on specified criteria using LLM.

All LLM tools in this package use a standardized output format which, for this tool, is:
{
  "status": "success|error",
  "tool_name": "rank_data",
  "timestamp": "ISO-8601 timestamp",
  "request_id": "unique_request_id",
  "execution_time": seconds,
  "metadata": {
    "user_prompt_for_task": "The user-provided prompt for item identification",
    "criteria_description_provided": "Original criteria string/dict given by user",
    "criteria_applied_summary_by_llm": "LLM's interpretation of how criteria were applied",
    "ranking_method_summary_by_llm": "LLM's description of its ranking method",
    "source_context_data_items_count": count_of_items_in_input_data,
    "identified_items_count": count_of_items_identified_by_llm_for_ranking,
    "ranked_items_count": count_of_items_actually_ranked,
    "average_score_of_ranked_items": average_score_if_applicable,
    "model_used": "model name"
  },
  "data": {
    "items_identified_for_ranking": [
        // List of items LLM identified for ranking
    ],
    "ranked_items": [
      {
        "rank": 1,
        "item": <identified_item_object_or_string>,
        "score": <numerical_score_0_to_100>,
        "explanation": <brief_explanation_for_this_item_s_rank_and_score>
      },
      // ... more ranked items
    ],
    "overall_ranking_summary": "LLM's overall summary of the ranking results for identified items",
    "top_item_details": { ... } // Details of the top-ranked item (rank:1)
  },
  "error": { // Only present if status is "error"
    "code": "error_code_or_type",
    "message": "detailed_error_message",
    "details": "optional_additional_details_or_stack_trace_snippet"
  }
}
"""

from typing import Dict, Any, List, Union

from .rank_data_tool import RankDataTool

# Create a default instance
_default_tool = RankDataTool()

async def rank_data(prompt: str, input_data: Union[List, Dict], criteria: Union[str, Dict]) -> Dict[str, Any]:
    """
    Identifies items from input_data based on a prompt and then ranks them based on criteria.

    Args:
        prompt: Instructions for how to use input_data (as context) and what to identify for ranking.
        input_data: List or dictionary of items to be used as context by the LLM.
        criteria: String description or dictionary of ranking criteria for the items identified by the LLM.

    Returns:
        Standardized output dictionary (see module docstring for detailed structure).
    """
    return await _default_tool.rank_data(prompt, input_data, criteria)


__all__ = []