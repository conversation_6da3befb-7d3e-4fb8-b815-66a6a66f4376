"""
Select Data module.

This module provides a simple interface to the Select Data tool.
The tool selects the best options from a list based on specified criteria,
guided by a user prompt.

All LLM tools in this package use a standardized output format:
{
  "status": "success|error",
  "tool_name": "select_data",
  "timestamp": "ISO-8601 timestamp",
  "request_id": "unique_request_id",
  "execution_time": seconds,
  "metadata": {
    "user_prompt_for_selection_task": "The user-provided prompt for selection",
    "criteria_applied": "description of how criteria were applied",
    "criteria_description": "original criteria",
    "topk": number of options selected,
    "options_count": total number of options considered,
    "explain": whether explanations were requested,
    "model_used": "model name"
  },
  "data": {
    "selected_options": [
      {
        "option": {...},  // The full option object
        "explanation": "explanation of selection",
        "confidence": 0.95  // Confidence score between 0 and 1
      },
      ...
    ],
    "alternatives_considered": [
      {
        "option": {...},  // Alternative that wasn't selected
        "reason_not_selected": "explanation"
      },
      ...
    ]
  }
}
"""

from typing import Dict, Any, List, Union

from .select_data_tool import SelectDataTool

# Create a default instance
_default_tool = SelectDataTool()

async def select_data(options: Union[List, Dict], criteria: Union[str, Dict],
               prompt: str, topk: int = 1, explain: bool = True) -> Dict[str, Any]:
    """
    Select options based on evaluation or criteria, guided by a prompt.

    Args:
        options: List or dictionary of options to select from
        criteria: String description or dictionary of selection criteria
        prompt: Instructions for how to use options and criteria for selection.
        topk: Number of top options to select (default: 1)
        explain: Whether to include explanations for selections (default: True)

    Returns:
        Standardized output dictionary with the following structure:
        {
          "status": "success|error",
          "tool_name": "select_data",
          "timestamp": "ISO-8601 timestamp",
          "request_id": "unique_request_id",
          "execution_time": seconds,
          "metadata": {
            "user_prompt_for_selection_task": "The user-provided prompt for selection",
            "criteria_applied": "description of how criteria were applied",
            "criteria_description": "original criteria",
            "topk": number of options selected,
            "options_count": total number of options considered,
            "explain": whether explanations were requested,
            "model_used": "model name"
          },
          "data": {
            "selected_options": [
              {
                "option": {...},  // The full option object
                "explanation": "explanation of selection",
                "confidence": 0.95  // Confidence score between 0 and 1
              },
              ...
            ],
            "alternatives_considered": [
              {
                "option": {...},  // Alternative that wasn't selected
                "reason_not_selected": "explanation"
              },
              ...
            ]
          }
        }
    """
    return await _default_tool.select_data(
        options=options,
        criteria=criteria,
        prompt=prompt,
        topk=topk,
        explain=explain
    )


__all__ = ['select_data']