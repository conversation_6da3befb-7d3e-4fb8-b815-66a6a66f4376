"""
SERP (Search Engine Results Page) search module.

This module provides a simple interface to the SERP API for searching
across multiple search engines (Google, Bing, Brave).
"""

import os
from typing import Dict, Any, List, Optional

from .serp_tool import SerpTool

# Create a default instance with environment variables
_default_tool = SerpTool()

def search(query: str, 
           engines: Optional[List[str]] = None, 
           max_results: int = 15,
           brave_api_key: Optional[str] = None,
           zyte_api_key: Optional[str] = None,
           **kwargs) -> Dict[str, Any]:
    """
    Search across multiple search engines.

    Args:
        query: Search query
        engines: List of search engine names to use (e.g., ["Google", "Bing", "Brave"])
        max_results: Maximum number of results to return
        brave_api_key: API key for Brave search
        zyte_api_key: API key for Zyte proxy service
        **kwargs: Additional search parameters

    Returns:
        Search results from multiple engines
    """
    # Create a custom instance if API keys are provided
    if brave_api_key or zyte_api_key:
        tool = SerpTool(
            brave_api_key=brave_api_key,
            zyte_api_key=zyte_api_key
        )
        return tool.search(query=query, engines=engines, max_results=max_results, **kwargs)
    
    # Otherwise use the default instance
    return _default_tool.search(query=query, engines=engines, max_results=max_results, **kwargs)

def get_available_engines() -> List[Dict[str, str]]:
    """
    Get a list of available search engines.

    Returns:
        List of available search engines
    """
    return _default_tool.get_available_engines()

def check_health() -> Dict[str, Any]:
    """
    Check if the SERP API is healthy.

    Returns:
        API health status
    """
    return _default_tool.check_health()


__all__ = ['search', 'check_health']