#!/usr/bin/env python3
"""
SERP Content Fetcher - A tool to fetch full content from search results

This tool extends the SERP tool by fetching the full content of each search result.
It uses the SERP API to get search results, then fetches and extracts content from each URL.
"""

import os
import sys
import json
import time
import requests
import re
from urllib.parse import urlparse
from bs4 import BeautifulSoup
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

class SerpContentFetcher:
    """
    A tool to fetch full content from search results
    """
    def __init__(self, base_url=None, api_key=None, zyte_api_key=None):
        """
        Initialize the SERP Content Fetcher
        
        Args:
            base_url: Base URL for the SERP API (default: http://localhost:9281)
            api_key: API key for the SERP API (optional)
            zyte_api_key: API key for Zyte (optional)
        """
        self.base_url = base_url or os.getenv("SERP_BASE_URL", "http://localhost:9281")
        self.api_key = api_key or os.getenv("SERP_API_KEY")
        self.zyte_api_key = zyte_api_key or os.getenv("ZYTE_API_KEY")
        self.session = requests.Session()
        self.session.headers.update({
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
            "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8",
            "Accept-Language": "en-US,en;q=0.5",
        })
    
    def _make_request(self, method, endpoint, data=None, params=None, timeout=30):
        """
        Make a request to the SERP API
        
        Args:
            method: HTTP method (GET, POST, etc.)
            endpoint: API endpoint
            data: Request data (for POST requests)
            params: Query parameters (for GET requests)
            timeout: Request timeout in seconds
            
        Returns:
            Response JSON
        """
        url = f"{self.base_url}{endpoint}"
        
        try:
            if method.upper() == "GET":
                response = self.session.get(url, params=params, timeout=timeout)
            elif method.upper() == "POST":
                response = self.session.post(url, json=data, timeout=timeout)
            else:
                raise ValueError(f"Unsupported HTTP method: {method}")
            
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            print(f"Error making request to {url}: {e}")
            return None
    
    def check_health(self):
        """
        Check if the SERP API is healthy
        
        Returns:
            Health status
        """
        return self._make_request("GET", "/")
    
    def get_available_engines(self):
        """
        Get available search engines
        
        Returns:
            List of available engines
        """
        return self._make_request("GET", "/engines")
    
    def search(self, query, engines=None, max_results=5, timeout_seconds=60, retries=1, **kwargs):
        """
        Search the web using the SERP API
        
        Args:
            query: Search query
            engines: List of search engines to use (default: ["Google"])
            max_results: Maximum number of results to return
            timeout_seconds: Request timeout in seconds
            retries: Number of retries on failure
            **kwargs: Additional parameters to pass to the API
            
        Returns:
            Search results
        """
        # Default to Google if no engines specified
        if not engines:
            engines = ["Google"]
        
        # Prepare request data
        data = {
            "query": query,
            "engines": engines,
            "max_results": max_results
        }
        
        # Add API keys if available
        if self.api_key:
            data["api_key"] = self.api_key
        
        if self.zyte_api_key:
            data["zyte_api_key"] = self.zyte_api_key
        
        # Add any additional parameters
        data.update(kwargs)
        
        # Set a custom timeout for this request
        custom_timeout = timeout_seconds
        
        # Implement retry logic
        last_error = None
        for attempt in range(retries + 1):
            try:
                # Make the request with custom timeout
                return self._make_request("POST", "/search", data=data, timeout=custom_timeout)
            except Exception as e:
                last_error = e
                if attempt < retries:
                    print(f"Attempt {attempt + 1} failed: {e}. Retrying...")
                    # Exponential backoff
                    time.sleep(2 ** attempt)
        
        # If we get here, all retries failed
        raise Exception(f"All {retries + 1} attempts failed. Last error: {last_error}")
    
    def fetch_content(self, url, timeout=30):
        """
        Fetch and extract content from a URL
        
        Args:
            url: URL to fetch
            timeout: Request timeout in seconds
            
        Returns:
            Dictionary with extracted content
        """
        try:
            response = self.session.get(url, timeout=timeout)
            response.raise_for_status()
            
            # Check content type
            content_type = response.headers.get('Content-Type', '').lower()
            
            # Handle HTML content
            if 'text/html' in content_type:
                return self._extract_html_content(response.text, url)
            # Handle PDF content
            elif 'application/pdf' in content_type:
                return self._extract_pdf_content(response.content, url)
            # Handle other content types
            else:
                return {
                    "url": url,
                    "content_type": content_type,
                    "content": f"Content type {content_type} not supported for extraction"
                }
        except Exception as e:
            return {
                "url": url,
                "error": str(e),
                "content": f"Error fetching content: {str(e)}"
            }
    
    def _extract_html_content(self, html, url):
        """
        Extract content from HTML
        
        Args:
            html: HTML content
            url: Source URL
            
        Returns:
            Dictionary with extracted content
        """
        try:
            soup = BeautifulSoup(html, 'html.parser')
            
            # Extract title
            title = ""
            if soup.title:
                title = soup.title.string.strip()
            
            # Extract meta description
            meta_description = ""
            meta_tag = soup.find('meta', attrs={'name': 'description'})
            if meta_tag and meta_tag.get('content'):
                meta_description = meta_tag.get('content').strip()
            
            # Extract main content
            main_content = self._extract_main_content(soup)
            
            # Extract text content
            text_content = soup.get_text(separator=' ', strip=True)
            
            return {
                "url": url,
                "title": title,
                "meta_description": meta_description,
                "main_content": main_content,
                "text_content": text_content[:5000],  # Limit text content to 5000 chars
                "content_type": "text/html"
            }
        except Exception as e:
            return {
                "url": url,
                "error": str(e),
                "content": f"Error extracting HTML content: {str(e)}",
                "content_type": "text/html"
            }
    
    def _extract_main_content(self, soup):
        """
        Extract main content from HTML
        
        Args:
            soup: BeautifulSoup object
            
        Returns:
            Extracted main content
        """
        # Try to find main content container
        main_content_tags = [
            'main', 'article', 'content', 'post-content', 'entry-content',
            'main-content', 'page-content', 'blog-content'
        ]
        
        # Look for content by ID or class
        for tag in main_content_tags:
            content = soup.find(id=tag) or soup.find(class_=tag) or soup.find(tag)
            if content:
                return content.get_text(separator='\n\n', strip=True)
        
        # If no main content found, extract paragraphs
        paragraphs = []
        for p in soup.find_all('p'):
            text = p.get_text(strip=True)
            if text and len(text) > 20:  # Only keep substantial paragraphs
                paragraphs.append(text)
        
        return '\n\n'.join(paragraphs)
    
    def _extract_pdf_content(self, pdf_bytes, url):
        """
        Extract content from PDF
        
        Args:
            pdf_bytes: PDF content as bytes
            url: Source URL
            
        Returns:
            Dictionary with extracted content
        """
        try:
            # Try to import PyPDF2
            import io
            from PyPDF2 import PdfReader
            
            pdf = PdfReader(io.BytesIO(pdf_bytes))
            content = []
            
            # Extract text from each page
            for i, page in enumerate(pdf.pages):
                text = page.extract_text()
                if text:
                    content.append(f"--- Page {i+1} ---\n{text}")
            
            return {
                "url": url,
                "title": os.path.basename(urlparse(url).path),
                "content": "\n\n".join(content),
                "content_type": "application/pdf",
                "num_pages": len(pdf.pages)
            }
        except ImportError:
            return {
                "url": url,
                "error": "PyPDF2 not installed",
                "content": "PDF extraction requires PyPDF2. Install with: pip install PyPDF2",
                "content_type": "application/pdf"
            }
        except Exception as e:
            return {
                "url": url,
                "error": str(e),
                "content": f"Error extracting PDF content: {str(e)}",
                "content_type": "application/pdf"
            }
    
    def search_and_fetch_content(self, query, engines=None, max_results=5, timeout_seconds=60, retries=1, **kwargs):
        """
        Search the web and fetch content from each result
        
        Args:
            query: Search query
            engines: List of search engines to use (default: ["Google"])
            max_results: Maximum number of results to return
            timeout_seconds: Request timeout in seconds
            retries: Number of retries on failure
            **kwargs: Additional parameters to pass to the API
            
        Returns:
            Search results with full content
        """
        # Search for results
        results = self.search(query, engines, max_results, timeout_seconds, retries, **kwargs)
        
        if not results or 'results' not in results:
            return results
        
        # Fetch content for each result
        for i, result in enumerate(results['results']):
            print(f"Fetching content for result {i+1}/{len(results['results'])}: {result.get('title')}")
            
            # Skip if no link
            if not result.get('link'):
                result['content'] = "No URL to fetch content from"
                continue
            
            # Fetch content
            content = self.fetch_content(result['link'])
            
            # Add content to result
            result['full_content'] = content
        
        return results
    
    def save_results_to_files(self, results, output_dir="results"):
        """
        Save search results to files
        
        Args:
            results: Search results
            output_dir: Output directory
            
        Returns:
            List of saved file paths
        """
        # Create output directory if it doesn't exist
        os.makedirs(output_dir, exist_ok=True)
        
        saved_files = []
        
        # Save query results to JSON file
        query = results.get('query', 'search')
        safe_query = re.sub(r'[^\w\s-]', '', query).strip().replace(' ', '-')
        json_file = os.path.join(output_dir, f"{safe_query}.json")
        
        with open(json_file, 'w') as f:
            json.dump(results, f, indent=2)
        
        saved_files.append(json_file)
        
        # Save each result to a markdown file
        if 'results' in results:
            for i, result in enumerate(results['results']):
                title = result.get('title', f"Result {i+1}")
                safe_title = re.sub(r'[^\w\s-]', '', title).strip().replace(' ', '-')[:50]
                md_file = os.path.join(output_dir, f"{i+1}_{safe_title}.md")
                
                with open(md_file, 'w') as f:
                    f.write(f"# {title}\n\n")
                    f.write(f"URL: {result.get('link')}\n\n")
                    
                    # Add description if available
                    if result.get('description'):
                        f.write(f"## Description\n\n{result.get('description')}\n\n")
                    
                    # Add content if available
                    if 'full_content' in result and result['full_content'].get('main_content'):
                        f.write(f"## Content\n\n{result['full_content']['main_content']}\n\n")
                    elif 'full_content' in result and result['full_content'].get('content'):
                        f.write(f"## Content\n\n{result['full_content']['content']}\n\n")
                
                saved_files.append(md_file)
        
        return saved_files


# Example usage
if __name__ == "__main__":
    # Parse command line arguments
    import argparse
    
    parser = argparse.ArgumentParser(description="SERP Content Fetcher")
    parser.add_argument("query", help="Search query")
    parser.add_argument("--engines", nargs="+", default=["Google"], help="Search engines to use")
    parser.add_argument("--max-results", type=int, default=5, help="Maximum number of results")
    parser.add_argument("--timeout", type=int, default=60, help="Request timeout in seconds")
    parser.add_argument("--retries", type=int, default=1, help="Number of retries on failure")
    parser.add_argument("--output-dir", default="results", help="Output directory")
    
    args = parser.parse_args()
    
    # Create the SERP Content Fetcher
    fetcher = SerpContentFetcher()
    
    try:
        # Check if the API is healthy
        health = fetcher.check_health()
        print(f"API Health: {health}")
        
        # Get available engines
        engines = fetcher.get_available_engines()
        print(f"Available engines: {engines}")
        
        # Search and fetch content
        results = fetcher.search_and_fetch_content(
            query=args.query,
            engines=args.engines,
            max_results=args.max_results,
            timeout_seconds=args.timeout,
            retries=args.retries
        )
        
        # Save results to files
        saved_files = fetcher.save_results_to_files(results, args.output_dir)
        
        print(f"\nSearch results for '{args.query}':")
        print(f"Query: {results.get('query')}")
        print(f"Engines: {results.get('engines')}")
        print(f"Result count: {results.get('count')}")
        
        # Print saved files
        print(f"\nSaved {len(saved_files)} files to {args.output_dir}:")
        for file in saved_files:
            print(f"- {file}")
        
    except Exception as e:
        print(f"Error: {e}")
