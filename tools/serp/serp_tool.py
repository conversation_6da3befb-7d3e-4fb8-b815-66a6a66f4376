#!/usr/bin/env python3
"""
SERP (Search Engine Results Page) search tool consumer.
"""

import os
from typing import Dict, Any, List, Optional

try:
    # When imported as a module
    from ..base_tool import BaseTool
except ImportError:
    # When run directly
    import sys
    import os
    sys.path.append(os.path.dirname(os.path.dirname(__file__)))
    from base_tool import BaseTool


class SerpTool(BaseTool):
    """
    Tool for searching multiple search engines using the SERP API.
    """

    def __init__(self,
                base_url: str = None,
                timeout: int = 120,  # Increased timeout for Zyte proxy
                brave_api_key: Optional[str] = None,
                zyte_api_key: Optional[str] = None,
                env_file: str = None):
        """
        Initialize the SERP search tool.

        Args:
            base_url: Base URL for the SERP API
            timeout: Default request timeout in seconds
            brave_api_key: API key for Brave search
            zyte_api_key: API key for Zyte proxy service (recommended for reliable results)
            env_file: Path to .env file

        Note:
            The Zyte API key is important for reliable results as it uses proxies to avoid being blocked.
        """
        # Get base URL from environment variable if not provided
        if base_url is None:
            base_url = os.environ.get("SERP_BASE_URL", "http://localhost:9281")
        
        # Initialize base class (this will load environment variables)
        super().__init__(base_url, timeout, env_file)

        # Get API keys from parameters or environment variables
        self.brave_api_key = brave_api_key or os.environ.get("BRAVE_API_KEY", "")
        self.zyte_api_key = zyte_api_key or os.environ.get("ZYTE_API_KEY", "")

    def get_available_engines(self) -> List[Dict[str, str]]:
        """
        Get a list of available search engines.

        Returns:
            List of available search engines
        """
        response = self._make_request("GET", "/engines")
        return response.get("engines", [])

    def search(self,
              query: str,
              engines: Optional[List[str]] = None,
              max_results: int = 15,
              default_delay_ms: int = 0,
              timeout_seconds: int = 120,
              retries: int = 2,
              **kwargs) -> Dict[str, Any]:
        """
        Search across multiple search engines.

        Args:
            query: Search query
            engines: List of search engine names to use (e.g., ["Google", "Bing", "Brave"])
            max_results: Maximum number of results to return
            default_delay_ms: Default delay between requests in milliseconds
            timeout_seconds: Timeout for the search request in seconds
            retries: Number of retries if the request fails
            **kwargs: Additional search parameters

        Returns:
            Search results from multiple engines
        """
        # Prepare request data
        data = {
            "query": query,
            "max_results": max_results,
            "default_delay_ms": default_delay_ms
        }

        # Add engines if specified
        if engines:
            data["engines"] = engines
        else:
            # Default to Google for faster results
            data["engines"] = ["Google"]

        # Add API keys
        if self.brave_api_key:
            data["brave_api_key"] = self.brave_api_key
        if self.zyte_api_key:
            data["zyte_api_key"] = self.zyte_api_key

        # Add any additional parameters
        data.update(kwargs)

        # Set a custom timeout for this request
        custom_timeout = timeout_seconds

        # Implement retry logic
        last_error = None
        for attempt in range(retries + 1):
            try:
                # Make the request with custom timeout
                return self._make_request("POST", "/search", data=data, timeout=custom_timeout)
            except Exception as e:
                last_error = e
                if attempt < retries:
                    print(f"Attempt {attempt + 1} failed: {e}. Retrying...")
                    # Exponential backoff
                    import time
                    time.sleep(2 ** attempt)

        # If we get here, all retries failed
        raise Exception(f"All {retries + 1} attempts failed. Last error: {last_error}")


# Example usage
if __name__ == "__main__":
    # Create the SERP search tool
    serp_tool = SerpTool()

    try:
        # Check if the API is healthy
        health = serp_tool.check_health()
        print(f"API Health: {health}")

        # Get available engines
        engines = serp_tool.get_available_engines()
        print(f"Available engines: {engines}")

        # Perform a search
        results = serp_tool.search(
            query="python web development",
            engines=["Google", "Bing", "Brave"],  # Using all available engines
            max_results=5,
            timeout_seconds=120,  # Longer timeout
            retries=2  # Enable retries
        )

        # Print search results
        print(f"\nSearch results for 'python web development':")
        print(f"Query: {results.get('query')}")
        print(f"Engines: {results.get('engines')}")
        print(f"Result count: {results.get('count')}")

        # Debug: Print the structure of the results
        print(f"Response keys: {list(results.keys())}")

        # Print the full response for debugging
        import json
        print(f"\nFull response: {json.dumps(results, indent=2)}")

        # Check if results field exists and is not None
        results_list = results.get('results')
        if results_list is not None and len(results_list) > 0:
            # Print first few results
            for i, result in enumerate(results_list):
                print(f"\nResult {i+1}:")
                print(f"Title: {result.get('title')}")
                print(f"Link: {result.get('link')}")
                print(f"Description: {result.get('description', '')}")

                # Print content if available
                if result.get('main_content'):
                    print(f"\nContent Preview (first 500 chars):")
                    content = result.get('main_content')
                    if len(content) > 500:
                        content_preview = content[:500] + "..."
                    else:
                        content_preview = content
                    print(f"{content_preview}")

                # Print scrape duration if available
                if result.get('page_scrape_duration'):
                    print(f"Scrape Duration: {result.get('page_scrape_duration')}")

                # Print scrape error if available
                if result.get('page_scrape_error'):
                    print(f"Scrape Error: {result.get('page_scrape_error')}")

                # Save full content to a file if main_content is available
                if result.get('main_content'):
                    import os
                    import re

                    # Create a safe filename from the title
                    safe_title = re.sub(r'[^\w\s-]', '', result.get('title', 'result'))[:50]
                    safe_title = re.sub(r'[-\s]+', '-', safe_title).strip('-')

                    # Create results directory if it doesn't exist
                    os.makedirs('results', exist_ok=True)

                    # Save the full content to a file
                    filename = f"results/result_{i+1}_{safe_title}.md"
                    with open(filename, 'w') as f:
                        f.write(f"# {result.get('title')}\n\n")
                        f.write(f"URL: {result.get('link')}\n\n")
                        if result.get('description'):
                            f.write(f"## Description\n\n{result.get('description')}\n\n")
                        f.write(f"## Content\n\n{result.get('main_content')}\n")

                        # Add scrape metadata if available
                        if result.get('page_scrape_duration') or result.get('page_scrape_error'):
                            f.write(f"\n## Metadata\n\n")
                            if result.get('page_scrape_duration'):
                                f.write(f"Scrape Duration: {result.get('page_scrape_duration')}\n\n")
                            if result.get('page_scrape_error'):
                                f.write(f"Scrape Error: {result.get('page_scrape_error')}\n\n")

                    print(f"Saved content to {filename}")
        else:
            print("\nNo results found or results field is missing in the response.")

    except Exception as e:
        print(f"Error: {e}")
