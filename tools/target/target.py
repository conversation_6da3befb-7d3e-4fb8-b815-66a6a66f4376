#!/usr/bin/env python3
"""
Target functional interface.

This module provides functional interfaces for the Target tool.
"""

from typing import Dict, Any, Optional
from .target_tool import TargetTool

# Global instance for functional interface
_target_tool = None

def get_tool(zyte_api_key: Optional[str] = None) -> TargetTool:
    """
    Get or create a Target tool instance.
    
    Args:
        zyte_api_key: Optional Zyte API key
        
    Returns:
        TargetTool: Target tool instance
    """
    global _target_tool
    if _target_tool is None:
        _target_tool = TargetTool(zyte_api_key=zyte_api_key)
    return _target_tool

def search(query: str, zyte_api_key: Optional[str] = None, **kwargs) -> Dict[str, Any]:
    """
    Search for products on Target.
    
    Args:
        query (str): Search query for Target products
        zyte_api_key: Optional Zyte API key
        **kwargs: Additional parameters
        
    Returns:
        Dict[str, Any]: Search results from Target
    """
    tool = get_tool(zyte_api_key)
    return tool.search(query, **kwargs)

def check_health(zyte_api_key: Optional[str] = None) -> Dict[str, Any]:
    """
    Check if the Target service is available.
    
    Args:
        zyte_api_key: Optional Zyte API key
        
    Returns:
        Dict[str, Any]: Health status
    """
    tool = get_tool(zyte_api_key)
    return tool.check_health()