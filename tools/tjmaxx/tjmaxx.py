#!/usr/bin/env python3
"""
TJMaxx functional interface.

This module provides functional interfaces for the TJMaxx tool.
"""

from typing import Dict, Any, Optional
from .tjmaxx_tool import TJMaxxTool

# Global instance for functional interface
_tjmaxx_tool = None

def get_tool(zyte_api_key: Optional[str] = None) -> TJMaxxTool:
    """
    Get or create a TJMaxx tool instance.
    
    Args:
        zyte_api_key: Optional Zyte API key
        
    Returns:
        TJMaxxTool: TJMaxx tool instance
    """
    global _tjmaxx_tool
    if _tjmaxx_tool is None:
        _tjmaxx_tool = TJMaxxTool(zyte_api_key=zyte_api_key)
    return _tjmaxx_tool

def search(query: str, zyte_api_key: Optional[str] = None, **kwargs) -> Dict[str, Any]:
    """
    Search for products on TJMaxx.
    
    Args:
        query (str): Search query for TJMaxx products
        zyte_api_key: Optional Zyte API key
        **kwargs: Additional parameters
        
    Returns:
        Dict[str, Any]: Search results from TJMaxx
    """
    tool = get_tool(zyte_api_key)
    return tool.search(query, **kwargs)

def check_health(zyte_api_key: Optional[str] = None) -> Dict[str, Any]:
    """
    Check if the TJMaxx service is available.
    
    Args:
        zyte_api_key: Optional Zyte API key
        
    Returns:
        Dict[str, Any]: Health status
    """
    tool = get_tool(zyte_api_key)
    return tool.check_health()