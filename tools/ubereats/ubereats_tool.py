#!/usr/bin/env python3
"""
UberEats search tool consumer.
"""

import os
from typing import Dict, Any, Optional

try:
    # When imported as a module
    from ..base_tool import BaseTool
except ImportError:
    # When run directly
    import sys
    import os
    sys.path.append(os.path.dirname(os.path.dirname(__file__)))
    from base_tool import BaseTool


class UberEatsTool(BaseTool):
    """
    Tool for searching UberEats restaurants using Zyte API and Google Maps API.
    """

    def __init__(self,
                zyte_api_key: Optional[str] = None,
                maps_api_key: Optional[str] = None,
                base_url: str = None,
                timeout: int = 360,
                env_file: str = None):
        """
        Initialize the UberEats search tool.

        Args:
            zyte_api_key: Zyte API key for authentication
            maps_api_key: Google Maps API key for geocoding
            base_url: Base URL for the UberEats API
            timeout: Request timeout in seconds
            env_file: Path to .env file
        """
        # Get base URL from environment variable if not provided
        if base_url is None:
            base_url = os.environ.get("UBEREATS_BASE_URL", "http://localhost:9298")
        
                # Initialize base class (this will load environment variables)
        super().__init__(base_url, timeout, env_file)
        self.zyte_api_key = zyte_api_key or os.getenv('ZYTE_API_KEY')
        self.maps_api_key = maps_api_key or os.getenv('GOOGLE_MAPS_API_KEY')

    def search(self, query: str, address: str, **kwargs) -> Dict[str, Any]:
        """
        Search for restaurants on UberEats.
        
        Args:
            query (str): Search query for restaurants/food
            address (str): Address for location-based search
            **kwargs: Additional parameters
            
        Returns:
            Dict[str, Any]: Search results from UberEats
        """
        try:
            params = {
                'query': query,
                'address': address
            }
            
            if self.zyte_api_key:
                params['zyte_api_key'] = self.zyte_api_key
            
            if self.maps_api_key:
                params['maps_api_key'] = self.maps_api_key
            
            # Add any additional parameters
            params.update(kwargs)
            
            response = self._make_request(
                method="GET",
                endpoint="/search",
                params=params
            )
            
            return response
            
        except Exception as e:
            return {
                'error': f"UberEats search failed: {str(e)}",
                'query': query,
                'address': address
            }
    
    def check_health(self) -> Dict[str, Any]:
        """
        Check if the UberEats service is available.
        
        Returns:
            Dict[str, Any]: Health status
        """
        try:
            response = self._make_request(
                method="GET",
                endpoint="/"
            )
            return {
                'status': 'healthy',
                'service': 'ubereats',
                'response': response
            }
        except Exception as e:
            return {
                'status': 'unhealthy',
                'service': 'ubereats',
                'error': str(e)
            }


# Example usage
if __name__ == "__main__":
    tool = UberEatsTool()
    
    try:
        # Check if the API is healthy
        health = tool.check_health()
        print(f"API Health: {health}")
        
        # Perform a search
        result = tool.search("pizza", "New York, NY")
        print(f"\nSearch results for 'pizza' in 'New York, NY':")
        print(result)
        
    except Exception as e:
        print(f"Error: {e}")