#!/usr/bin/env python3
"""
Walmart search module.

This module provides a simple interface to the Walmart search API.
"""

from typing import Dict, Any, Optional
from .walmart_tool import WalmartTool

# Global tool instance
_walmart_tool = None


def get_walmart_tool() -> WalmartTool:
    """
    Get or create a global Walmart tool instance.
    
    Returns:
        WalmartTool: The Walmart tool instance
    """
    global _walmart_tool
    if _walmart_tool is None:
        _walmart_tool = WalmartTool()
    return _walmart_tool


def search(query: str, **kwargs) -> Dict[str, Any]:
    """
    Search for products on Walmart.
    
    Args:
        query (str): Search query for Walmart products
        **kwargs: Additional search parameters
        
    Returns:
        Dict[str, Any]: Search results from Walmart
    """
    tool = get_walmart_tool()
    return tool.search(query, **kwargs)


def check_health() -> Dict[str, Any]:
    """
    Check if the Walmart service is available.
    
    Returns:
        Dict[str, Any]: Health status
    """
    tool = get_walmart_tool()
    return tool.check_health()


def get_available_endpoints() -> Dict[str, str]:
    """
    Get available Walmart API endpoints.
    
    Returns:
        Dict[str, str]: Available endpoints
    """
    return {
        'root': 'GET /',
        'search': 'POST /search'
    }