"""
Web search module.

This module provides a simple interface to the Web search API.
"""

from typing import Dict, Any, Optional

from .web_tool import WebTool

# Create a default instance with environment variables
_default_tool = WebTool()

def search(query: str,
           limit: int = 5,
           lang: str = "en",
           process_pdfs: bool = True,
           api_key: Optional[str] = None,
           **kwargs) -> Dict[str, Any]:
    """
    Search the web.

    Args:
        query: Search query
        limit: Maximum number of results to return (1-20)
        lang: Language for search results
        process_pdfs: Whether to process PDF links to extract content
        api_key: SerpAPI key
        **kwargs: Additional search parameters

    Returns:
        Web search results
    """
    # Create a custom instance if API key is provided
    if api_key:
        tool = WebTool(api_key=api_key)
        return tool.search(
            query=query,
            limit=limit,
            lang=lang,
            process_pdfs=process_pdfs,
            **kwargs
        )
    
    # Otherwise use the default instance
    return _default_tool.search(
        query=query,
        limit=limit,
        lang=lang,
        process_pdfs=process_pdfs,
        **kwargs
    )

def check_health() -> Dict[str, Any]:
    """
    Check if the Web API is healthy.

    Returns:
        API health status
    """
    return _default_tool.check_health()


__all__ = ['search', 'check_health']