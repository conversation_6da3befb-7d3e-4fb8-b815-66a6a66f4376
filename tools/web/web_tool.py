#!/usr/bin/env python3
"""
Web search tool consumer.
"""

import os
from typing import Dict, Any, List, Optional, Union

try:
    # When imported as a module
    from ..base_tool import BaseTool
except ImportError:
    # When run directly
    import sys
    import os
    sys.path.append(os.path.dirname(os.path.dirname(__file__)))
    from base_tool import BaseTool


class WebTool(BaseTool):
    """
    Tool for searching the web using SerpAPI.
    """

    def __init__(self,
                base_url: str = None,
                timeout: int = 60,
                api_key: Optional[str] = None,
                env_file: str = None):
        """
        Initialize the Web search tool.

        Args:
            base_url: Base URL for the Web API
            timeout: Request timeout in seconds
            api_key: SerpAPI key
            env_file: Path to .env file
        """
        # Get base URL from environment variable if not provided
        if base_url is None:
            base_url = os.environ.get("WEB_BASE_URL", "http://localhost:9288")
        
                # Initialize base class (this will load environment variables)
        super().__init__(base_url, timeout, env_file)

        # Get API key from parameter or environment variable
        self.api_key = api_key or os.environ.get("SERPAPI_KEY", "")

    def search(self,
              query: str,
              limit: int = 5,
              lang: str = "en",
              process_pdfs: bool = True,
              debug: bool = False,
              **kwargs) -> Dict[str, Any]:
        """
        Search the web.

        Args:
            query: Search query
            limit: Maximum number of results to return (1-20)
            lang: Language for search results
            process_pdfs: Whether to process PDF links to extract content
            debug: Enable debug mode
            **kwargs: Additional search parameters

        Returns:
            Web search results
        """
        # Prepare request parameters
        params = {
            "query": query,
            "limit": limit,
            "lang": lang,
            "process_pdfs": process_pdfs,
            "debug": debug
        }

        # Add API key if available
        if self.api_key:
            params["api_key"] = self.api_key

        # Add any additional parameters
        params.update(kwargs)

        # Make the request
        return self._make_request("GET", "/web/search", params=params)


# Example usage
if __name__ == "__main__":
    # Create the Web search tool
    web_tool = WebTool()

    try:
        # Check if the API is healthy
        health = web_tool.check_health()
        print(f"API Health: {health}")

        # Perform a search
        results = web_tool.search(
            query="climate change research",
            limit=3,
            process_pdfs=True
        )

        # Print search results
        print(f"\nSearch results for 'climate change research':")

        # Print results
        web_results = results.get('results', [])
        print(f"Results found: {len(web_results)}")

        # Print each result
        for i, result in enumerate(web_results):
            print(f"\nResult {i+1}:")
            print(f"Title: {result.get('title')}")
            print(f"Link: {result.get('link')}")
            print(f"Description: {result.get('description', '')}")
            
            # Print content if available
            if result.get('main_content'):
                print(f"\nContent Preview (first 200 chars):")
                content = result.get('main_content')
                if len(content) > 200:
                    content_preview = content[:200] + "..."
                else:
                    content_preview = content
                print(f"{content_preview}")

    except Exception as e:
        print(f"Error: {e}")
