"""
YouTube search module.

This module provides a simple interface to the YouTube search API.
"""

from typing import Dict, Any, Optional

from .youtube_tool import YouTubeTool

# Create a default instance with environment variables
_default_tool = YouTubeTool()

def search(query: str,
           limit: int = 5,
           api_key: Optional[str] = None,
           **kwargs) -> Dict[str, Any]:
    """
    Search for YouTube videos.

    Args:
        query: Search query
        limit: Maximum number of results to return (1-50)
        api_key: YouTube API key
        **kwargs: Additional search parameters

    Returns:
        YouTube video search results
    """
    # Create a custom instance if API key is provided
    if api_key:
        tool = YouTubeTool(api_key=api_key)
        return tool.search(query=query, limit=limit, **kwargs)
    
    # Otherwise use the default instance
    return _default_tool.search(query=query, limit=limit, **kwargs)

def check_health() -> Dict[str, Any]:
    """
    Check if the YouTube API is healthy.

    Returns:
        API health status
    """
    return _default_tool.check_health()


__all__ = ['search', 'check_health']