#!/usr/bin/env python3
"""
YouTube search tool consumer.
"""

import os
from typing import Dict, Any, List, Optional, Union

try:
    # When imported as a module
    from ..base_tool import BaseTool
except ImportError:
    # When run directly
    import sys
    import os
    sys.path.append(os.path.dirname(os.path.dirname(__file__)))
    from base_tool import BaseTool


class YouTubeTool(BaseTool):
    """
    Tool for searching YouTube videos.
    """

    def __init__(self,
                base_url: str = None,
                timeout: int = 60,
                youtube_api_key: Optional[str] = None,
                env_file: str = None):
        """
        Initialize the YouTube search tool.

        Args:
            base_url: Base URL for the YouTube API
            timeout: Request timeout in seconds
            youtube_api_key: YouTube API key
            env_file: Path to .env file
        """
        # Get base URL from environment variable if not provided
        if base_url is None:
            base_url = os.environ.get("YOUTUBE_BASE_URL", "http://localhost:9285")
        
                # Initialize base class (this will load environment variables)
        super().__init__(base_url, timeout, env_file)

        # Get API key from parameter or environment variable
        self.api_key = youtube_api_key or os.environ.get("YOUTUBE_API_KEY", "")

    def search(self,
              query: str,
              limit: int = 5,
              debug: bool = False,
              **kwargs) -> Dict[str, Any]:
        """
        Search for YouTube videos.

        Args:
            query: Search query
            limit: Maximum number of results to return (1-50)
            debug: Enable debug mode
            **kwargs: Additional search parameters

        Returns:
            Video search results
        """
        # Prepare request parameters
        params = {
            "query": query,
            "limit": limit,
            "debug": debug
        }

        # Add API key if available
        if self.api_key:
            params["api_key"] = self.api_key

        # Add any additional parameters
        params.update(kwargs)

        # Make the request
        return self._make_request("GET", "/videos/search", params=params)


# Example usage
if __name__ == "__main__":
    # Create the YouTube search tool
    youtube_tool = YouTubeTool()

    try:
        # Check if the API is healthy
        health = youtube_tool.check_health()
        print(f"API Health: {health}")

        # Perform a search
        results = youtube_tool.search(
            query="Python programming tutorial",
            limit=3
        )

        # Print search results
        print(f"\nSearch results for 'Python programming tutorial':")

        # Print videos
        videos = results.get('videos', [])
        print(f"Videos found: {len(videos)}")

        # Print each video
        for i, video in enumerate(videos):
            print(f"\nVideo {i+1}:")
            print(f"Title: {video.get('title')}")
            print(f"Link: {video.get('link')}")
            print(f"Views: {video.get('view_count')}")
            print(f"Likes: {video.get('like_count')}")
            print(f"Duration: {video.get('duration')}")
            print(f"Channel: {video.get('channel_title')}")
            print(f"Published: {video.get('published_at')}")

    except Exception as e:
        print(f"Error: {e}")
