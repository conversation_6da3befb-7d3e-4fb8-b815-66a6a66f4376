#!/usr/bin/env python3
"""
Zara functional interface.

This module provides functional interfaces for the Zara tool.
"""

from typing import Dict, Any, Optional
from .zara_tool import ZaraTool

# Global instance for functional interface
_zara_tool = None

def get_tool(zyte_api_key: Optional[str] = None) -> ZaraTool:
    """
    Get or create a Zara tool instance.
    
    Args:
        zyte_api_key: Optional Zyte API key
        
    Returns:
        ZaraTool: Zara tool instance
    """
    global _zara_tool
    if _zara_tool is None:
        _zara_tool = ZaraTool(zyte_api_key=zyte_api_key)
    return _zara_tool

def search(query: str, zyte_api_key: Optional[str] = None, **kwargs) -> Dict[str, Any]:
    """
    Search for products on Zara.
    
    Args:
        query (str): Search query for Zara products
        zyte_api_key: Optional Zyte API key
        **kwargs: Additional parameters
        
    Returns:
        Dict[str, Any]: Search results from Zara
    """
    tool = get_tool(zyte_api_key)
    return tool.search(query, **kwargs)

def check_health(zyte_api_key: Optional[str] = None) -> Dict[str, Any]:
    """
    Check if the Zara service is available.
    
    Args:
        zyte_api_key: Optional Zyte API key
        
    Returns:
        Dict[str, Any]: Health status
    """
    tool = get_tool(zyte_api_key)
    return tool.check_health()