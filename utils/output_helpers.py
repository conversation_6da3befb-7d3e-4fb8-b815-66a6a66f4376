"""
Output Helpers Utility.

This module provides utility functions for working with the standardized output format
used by LLM tools.
"""

import json
import logging
from typing import Any, Dict, List, Optional, Union, Tuple, TypeVar, Callable

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Type variable for generic functions
T = TypeVar('T')

def is_standardized_output(output: Any) -> bool:
    """
    Check if an output follows the standardized format.
    
    Args:
        output: The output to check
        
    Returns:
        True if the output follows the standardized format, False otherwise
    """
    if not isinstance(output, dict):
        return False
        
    # Check for required fields
    required_fields = ["status", "tool_name", "timestamp", "request_id", "execution_time"]
    for field in required_fields:
        if field not in output:
            return False
            
    # Check for data or error field
    if "data" not in output and "error" not in output:
        return False
        
    # Check for metadata field
    if "metadata" not in output:
        return False
        
    return True

def get_data(output: Any) -> Optional[Dict[str, Any]]:
    """
    Get the data section from a standardized output.
    
    Args:
        output: The standardized output
        
    Returns:
        The data section, or None if not found
    """
    if not is_standardized_output(output):
        logger.warning("Output is not in standardized format")
        return None
        
    return output.get("data")

def get_metadata(output: Any) -> Optional[Dict[str, Any]]:
    """
    Get the metadata section from a standardized output.
    
    Args:
        output: The standardized output
        
    Returns:
        The metadata section, or None if not found
    """
    if not is_standardized_output(output):
        logger.warning("Output is not in standardized format")
        return None
        
    return output.get("metadata")

def get_error(output: Any) -> Optional[Dict[str, Any]]:
    """
    Get the error section from a standardized output.
    
    Args:
        output: The standardized output
        
    Returns:
        The error section, or None if not found
    """
    if not is_standardized_output(output):
        logger.warning("Output is not in standardized format")
        return None
        
    return output.get("error")

def is_success(output: Any) -> bool:
    """
    Check if a standardized output indicates success.
    
    Args:
        output: The standardized output
        
    Returns:
        True if the output indicates success, False otherwise
    """
    if not is_standardized_output(output):
        logger.warning("Output is not in standardized format")
        return False
        
    return output.get("status") == "success"

def get_field(output: Any, field_path: str, default: T = None) -> Union[Any, T]:
    """
    Get a field from a standardized output using a dot-separated path.
    
    Args:
        output: The standardized output
        field_path: Dot-separated path to the field (e.g., "data.ranked_items[0].item")
        default: Default value to return if the field is not found
        
    Returns:
        The field value, or the default value if not found
    """
    if not is_standardized_output(output):
        logger.warning("Output is not in standardized format")
        return default
        
    # Import the reference resolver here to avoid circular imports
    from gaiav2.utils.reference_resolver import ReferenceResolver
    
    # Create a dummy results dictionary with the output
    results = {"results_of_dummy": output}
    
    # Create a reference resolver with the dummy results
    resolver = ReferenceResolver(results)
    
    # Resolve the reference
    reference = f"results_of_dummy.{field_path}"
    resolved_value = resolver.resolve_reference(reference)
    
    # Check if the reference was resolved
    if resolved_value is reference:
        logger.warning(f"Could not resolve field path '{field_path}'")
        return default
        
    return resolved_value

# Helper functions for specific tool outputs

def get_ranked_items(output: Any) -> List[Dict[str, Any]]:
    """
    Get the ranked items from a rank_data tool output.
    
    Args:
        output: The rank_data tool output
        
    Returns:
        List of ranked items, or an empty list if not found
    """
    if not is_standardized_output(output):
        logger.warning("Output is not in standardized format")
        return []
        
    data = get_data(output)
    if not data:
        return []
        
    return data.get("ranked_items", [])

def get_top_ranked_item(output: Any) -> Optional[Dict[str, Any]]:
    """
    Get the top-ranked item from a rank_data tool output.
    
    Args:
        output: The rank_data tool output
        
    Returns:
        The top-ranked item, or None if not found
    """
    ranked_items = get_ranked_items(output)
    if not ranked_items:
        return None
        
    return ranked_items[0]

def get_selected_options(output: Any) -> List[Dict[str, Any]]:
    """
    Get the selected options from a select_data tool output.
    
    Args:
        output: The select_data tool output
        
    Returns:
        List of selected options, or an empty list if not found
    """
    if not is_standardized_output(output):
        logger.warning("Output is not in standardized format")
        return []
        
    data = get_data(output)
    if not data:
        return []
        
    return data.get("selected_options", [])

def get_top_selected_option(output: Any) -> Optional[Dict[str, Any]]:
    """
    Get the top selected option from a select_data tool output.
    
    Args:
        output: The select_data tool output
        
    Returns:
        The top selected option, or None if not found
    """
    selected_options = get_selected_options(output)
    if not selected_options:
        return None
        
    return selected_options[0]

def get_final_answer(output: Any) -> Optional[str]:
    """
    Get the final answer from a generate_final_answers tool output.
    
    Args:
        output: The generate_final_answers tool output
        
    Returns:
        The final answer, or None if not found
    """
    if not is_standardized_output(output):
        logger.warning("Output is not in standardized format")
        return None
        
    data = get_data(output)
    if not data:
        return None
        
    return data.get("answer")

def get_summary(output: Any) -> Optional[str]:
    """
    Get the summary from a tool output.
    
    Args:
        output: The tool output
        
    Returns:
        The summary, or None if not found
    """
    if not is_standardized_output(output):
        logger.warning("Output is not in standardized format")
        return None
        
    data = get_data(output)
    if not data:
        return None
        
    return data.get("summary")

def get_recommendations(output: Any) -> List[str]:
    """
    Get the recommendations from a tool output.
    
    Args:
        output: The tool output
        
    Returns:
        List of recommendations, or an empty list if not found
    """
    if not is_standardized_output(output):
        logger.warning("Output is not in standardized format")
        return []
        
    data = get_data(output)
    if not data:
        return []
        
    return data.get("recommendations", [])
