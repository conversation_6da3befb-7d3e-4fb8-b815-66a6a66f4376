"""
Reference Resolver Utility.

This module provides utilities for resolving references to task outputs,
especially for the standardized output format used by LLM tools.
"""

import re
import json
import logging
from typing import Any, Dict, List, Optional, Union, Tuple

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ReferenceResolver:
    """
    Utility class for resolving references to task outputs.

    This class provides methods for resolving references to task outputs,
    including support for nested fields, array indexing, property access,
    and template variables.
    """

    def __init__(self, results: Dict[str, Any]):
        """
        Initialize the reference resolver.

        Args:
            results: Dictionary of task results
        """
        self.results = results

    def resolve_template_variables(self, value: str) -> str:
        """
        Resolve template variables in a string.

        This method handles template variables in the format {{variable_name}}.
        It attempts to find the variable in the results dictionary and replace
        it with the corresponding value.

        Args:
            value: String containing template variables

        Returns:
            String with template variables replaced by their values
        """
        if not isinstance(value, str):
            return value

        # Check if the string contains template variables
        if "{{" not in value or "}}" not in value:
            return value

        # Extract template variables
        import re
        template_vars = re.findall(r'{{(.*?)}}', value)

        if not template_vars:
            return value

        # Create a copy of the string to modify
        result = value

        # Process each template variable
        for var_name in template_vars:
            var_name = var_name.strip()

            # Try to find the variable in the results
            replacement = None

            # Special case for "top_city_X" format
            if var_name.startswith("top_city_"):
                try:
                    # Extract the index (e.g., 5 from "top_city_5")
                    index = int(var_name.split("_")[-1]) - 1  # Convert 1-based to 0-based

                    # Check all task results for ranked_items or cities
                    for task_id, task_result in self.results.items():
                        if not isinstance(task_result, dict) or "data" not in task_result:
                            continue

                        data = task_result["data"]

                        # Check for city_X field
                        city_key = f"city_{index + 1}"
                        if city_key in data:
                            replacement = data[city_key]
                            logger.info(f"Found template variable '{var_name}' as {task_id}.data.{city_key}")
                            break

                        # Check for top_cities array
                        if "top_cities" in data and isinstance(data["top_cities"], list):
                            if 0 <= index < len(data["top_cities"]):
                                replacement = data["top_cities"][index]
                                logger.info(f"Found template variable '{var_name}' as {task_id}.data.top_cities[{index}]")
                                break

                        # Check for ranked_items array
                        if "ranked_items" in data and isinstance(data["ranked_items"], list):
                            if 0 <= index < len(data["ranked_items"]):
                                item = data["ranked_items"][index]
                                if isinstance(item, dict) and "item" in item and isinstance(item["item"], dict):
                                    if "name" in item["item"]:
                                        replacement = item["item"]["name"]
                                        logger.info(f"Found template variable '{var_name}' as {task_id}.data.ranked_items[{index}].item.name")
                                        break
                except (ValueError, IndexError):
                    pass

            # If we still don't have a replacement, check all task results for a field matching the variable name
            if replacement is None:
                for task_id, task_result in self.results.items():
                    if not isinstance(task_result, dict):
                        continue

                    # Check if the variable exists directly in the result
                    if var_name in task_result:
                        replacement = task_result[var_name]
                        logger.info(f"Found template variable '{var_name}' in {task_id}")
                        break

                    # Check if the variable exists in the data section
                    if "data" in task_result and isinstance(task_result["data"], dict):
                        if var_name in task_result["data"]:
                            replacement = task_result["data"][var_name]
                            logger.info(f"Found template variable '{var_name}' in {task_id}.data")
                            break

                    # Check for array items with a name field matching the variable
                    if "data" in task_result and isinstance(task_result["data"], dict):
                        for field_name in ["items", "results", "ranked_items", "selected_options", "cities", "hotels", "activities"]:
                            if field_name in task_result["data"] and isinstance(task_result["data"][field_name], list):
                                items = task_result["data"][field_name]

                                # Check if any item has a name or id matching the variable
                                for i, item in enumerate(items):
                                    if isinstance(item, dict):
                                        # Check for direct match with item name or id
                                        if item.get("name") == var_name or item.get("id") == var_name:
                                            replacement = item
                                            logger.info(f"Found template variable '{var_name}' as item in {task_id}.data.{field_name}[{i}]")
                                            break

                                        # Check for numbered variables like "city_1", "hotel_2", etc.
                                        if var_name.startswith(field_name[:-1] + "_"):  # e.g., "city_" from "cities"
                                            try:
                                                index = int(var_name.split("_")[-1]) - 1  # Convert 1-based to 0-based
                                                if 0 <= index < len(items):
                                                    replacement = items[index]
                                                    logger.info(f"Found template variable '{var_name}' as indexed item {task_id}.data.{field_name}[{index}]")
                                                    break
                                            except (ValueError, IndexError):
                                                pass

                                        # Check for variables like "top_city_5", "best_hotel_2", etc.
                                        if var_name.startswith("top_") and var_name.endswith("_" + str(i + 1)):
                                            # Extract the middle part (e.g., "city" from "top_city_5")
                                            middle_part = var_name[4:-2]
                                            if middle_part == field_name[:-1]:  # e.g., "city" matches "cities"
                                                replacement = item
                                                logger.info(f"Found template variable '{var_name}' as item {task_id}.data.{field_name}[{i}]")
                                                break

                            if replacement is not None:
                                break

            # If we found a replacement, replace the template variable
            if replacement is not None:
                # Convert the replacement to a string if it's not already
                if not isinstance(replacement, str):
                    if isinstance(replacement, dict):
                        # If it's a dict with a name field, use that
                        if "name" in replacement:
                            replacement = replacement["name"]
                        # Otherwise, use the first string field we find
                        else:
                            for key, val in replacement.items():
                                if isinstance(val, str):
                                    replacement = val
                                    break
                            # If we still don't have a string, convert the whole dict to a string
                            if not isinstance(replacement, str):
                                import json
                                replacement = json.dumps(replacement)
                    else:
                        replacement = str(replacement)

                # Replace the template variable in the string
                result = result.replace(f"{{{{{var_name}}}}}", replacement)
                logger.info(f"Replaced template variable '{var_name}' with '{replacement}'")
            else:
                logger.warning(f"Could not find a value for template variable '{var_name}'")

        return result

    def resolve_reference(self, reference: str) -> Any:
        """
        Resolve a reference to a task output.

        This method supports various reference formats:
        - results_of_X: Direct reference to results of task X
        - output_of_X: Alternative reference to results of task X
        - field_from_X: Reference to a specific field in the results of task X
        - results_of_X.data.field: Reference to a nested field in the results of task X
        - results_of_X.data.array[0]: Reference to an array element in the results of task X
        - results_of_X.data.array[0].field: Reference to a field in an array element
        - {{variable_name}}: Template variable format (will be resolved by searching all task results)

        Args:
            reference: Reference string

        Returns:
            Resolved value or the original reference if it cannot be resolved
        """
        if not isinstance(reference, str):
            return reference

        # First, check if this is a template variable format
        if "{{" in reference and "}}" in reference:
            resolved = self.resolve_template_variables(reference)
            if resolved != reference:
                return resolved

        # Handle different reference formats

        # 1. Direct reference to results (results_of_X)
        if reference.startswith("results_of_"):
            base_key = reference
            path = []

            # Check for nested field access with dot notation
            if "." in reference:
                parts = reference.split(".", 1)
                base_key = parts[0]
                path = self._parse_path(parts[1])

            # Check if the base key exists in results
            if base_key in self.results:
                result = self.results[base_key]

                # If there's a path, traverse it
                if path:
                    try:
                        return self._traverse_path(result, path)
                    except (KeyError, IndexError, TypeError) as e:
                        logger.warning(f"Error traversing path '{'.'.join(str(p) for p in path)}' in reference '{reference}': {e}")
                        return reference

                return result
            else:
                # Special case for ranked_items references
                if reference.endswith(".ranked_items"):
                    task_id = reference.replace("results_of_", "").replace(".ranked_items", "")
                    # Check if we have results for this task
                    task_key = f"results_of_{task_id}"
                    if task_key in self.results:
                        result = self.results[task_key]
                        # Check if the result has a data field with ranked_items
                        if isinstance(result, dict) and "data" in result and isinstance(result["data"], dict) and "ranked_items" in result["data"]:
                            logger.info(f"Resolved reference '{reference}' to 'data.ranked_items' in {task_key}")
                            return result["data"]["ranked_items"]
                        # Check if the result has ranked_items directly
                        elif isinstance(result, dict) and "ranked_items" in result:
                            logger.info(f"Resolved reference '{reference}' to 'ranked_items' in {task_key}")
                            return result["ranked_items"]

                # Special case for selected_options references
                if ".selected_options[" in reference:
                    # Extract the task ID and index
                    match = re.search(r'results_of_(.*?)\.selected_options\[(\d+)\]', reference)
                    if match:
                        task_id = match.group(1)
                        index = int(match.group(2))

                        # Check if we have results for this task
                        task_key = f"results_of_{task_id}"
                        if task_key in self.results:
                            result = self.results[task_key]
                            # Check if the result has a data field with selected_options
                            if isinstance(result, dict) and "data" in result and isinstance(result["data"], dict) and "selected_options" in result["data"]:
                                selected_options = result["data"]["selected_options"]
                                if isinstance(selected_options, list) and 0 <= index < len(selected_options):
                                    logger.info(f"Resolved reference '{reference}' to 'data.selected_options[{index}]' in {task_key}")
                                    return selected_options[index]
                            # Check if the result has selected_options directly
                            elif isinstance(result, dict) and "selected_options" in result:
                                selected_options = result["selected_options"]
                                if isinstance(selected_options, list) and 0 <= index < len(selected_options):
                                    logger.info(f"Resolved reference '{reference}' to 'selected_options[{index}]' in {task_key}")
                                    return selected_options[index]

                logger.warning(f"Reference to unknown result: {reference}")
                return reference

        # 2. Alternative reference format (output_of_X)
        elif reference.startswith("output_of_"):
            # Convert to results_of_X format
            task_id = reference.replace("output_of_", "")
            base_key = f"results_of_{task_id}"
            path = []

            # Check for nested field access with dot notation
            if "." in task_id:
                parts = task_id.split(".", 1)
                base_key = f"results_of_{parts[0]}"
                path = self._parse_path(parts[1])

            # Check if the base key exists in results
            if base_key in self.results:
                result = self.results[base_key]

                # If there's a path, traverse it
                if path:
                    try:
                        return self._traverse_path(result, path)
                    except (KeyError, IndexError, TypeError) as e:
                        logger.warning(f"Error traversing path '{'.'.join(str(p) for p in path)}' in reference '{reference}': {e}")
                        return reference

                return result
            else:
                logger.warning(f"Reference to unknown result: {reference} (looking for {base_key})")
                return reference

        # 3. Field reference format (field_from_X)
        elif "_from_" in reference:
            parts = reference.split("_from_")
            if len(parts) == 2:
                field, task_id = parts
                base_key = f"results_of_{task_id}"

                # Check for nested field access with dot notation
                path = []
                if "." in task_id:
                    task_parts = task_id.split(".", 1)
                    base_key = f"results_of_{task_parts[0]}"
                    path = self._parse_path(task_parts[1])

                # Check if the base key exists in results
                if base_key in self.results:
                    result = self.results[base_key]

                    # If there's a path, traverse it first
                    if path:
                        try:
                            result = self._traverse_path(result, path)
                        except (KeyError, IndexError, TypeError) as e:
                            logger.warning(f"Error traversing path '{'.'.join(str(p) for p in path)}' in reference '{reference}': {e}")
                            return reference

                    # Now check for the field in the result
                    if isinstance(result, dict):
                        # Handle standardized output format
                        if "data" in result and isinstance(result["data"], dict):
                            # Check if the field exists in the data section
                            if field in result["data"]:
                                logger.info(f"Resolved reference '{reference}' to field '{field}' in data section of {base_key}")
                                return result["data"][field]

                        # Check if the field exists directly in the result
                        if field in result:
                            logger.info(f"Resolved reference '{reference}' to field '{field}' in {base_key}")
                            return result[field]

                        # Special case for "ranked_items" field
                        if field.startswith("ranked_") and "ranked_items" in result:
                            logger.info(f"Resolved reference '{reference}' to 'ranked_items' in {base_key}")
                            return result["ranked_items"]

                        # Special case for standardized output format
                        if field == "data" and "data" in result:
                            logger.info(f"Resolved reference '{reference}' to 'data' section in {base_key}")
                            return result["data"]

                        if field == "metadata" and "metadata" in result:
                            logger.info(f"Resolved reference '{reference}' to 'metadata' section in {base_key}")
                            return result["metadata"]

                    # If we couldn't find the specific field, return the whole result
                    logger.info(f"Could not find field '{field}' in {base_key}, returning full result")
                    return result
                else:
                    logger.warning(f"Reference to unknown result: {reference} (looking for {base_key})")
                    return reference

        # If no reference pattern matches, return the original value
        return reference

    def _parse_path(self, path_str: str) -> List[Union[str, int]]:
        """
        Parse a path string into a list of path components.

        Handles array indexing (e.g., array[0]) and nested fields (e.g., field1.field2).

        Args:
            path_str: Path string (e.g., "data.items[0].name")

        Returns:
            List of path components (e.g., ["data", "items", 0, "name"])
        """
        if not path_str:
            return []

        # Use regex to split the path into components
        # This handles both dot notation and array indexing
        components = []

        # First, tokenize the path string
        tokens = []
        i = 0
        while i < len(path_str):
            if path_str[i] == '.':
                # Dot separator
                i += 1
            elif path_str[i] == '[':
                # Array indexing
                start = i
                while i < len(path_str) and path_str[i] != ']':
                    i += 1
                if i < len(path_str):  # Found closing bracket
                    tokens.append(path_str[start:i+1])
                    i += 1
            else:
                # Field name
                start = i
                while i < len(path_str) and path_str[i] != '.' and path_str[i] != '[':
                    i += 1
                tokens.append(path_str[start:i])

        # Process tokens into components
        for token in tokens:
            if '[' in token and token.endswith(']'):
                # Array indexing
                parts = token.split('[')
                field = parts[0]
                if field:  # Non-empty field name
                    components.append(field)
                # Process all indices
                for part in parts[1:]:
                    if part.endswith(']'):
                        try:
                            index = int(part[:-1])
                            components.append(index)
                        except ValueError:
                            # If not a valid integer index, treat as a string
                            components.append(part[:-1])
            else:
                # Regular field
                components.append(token)

        return components

    def _process_array_indexing(self, component: str) -> List[Union[str, int]]:
        """
        Process array indexing in a path component.

        Args:
            component: Path component (e.g., "items[0]")

        Returns:
            List of path components (e.g., ["items", 0])
        """
        # Check for array indexing
        if '[' not in component or not component.endswith(']'):
            return [component]

        parts = component.split('[')
        field = parts[0]
        result = [field] if field else []

        # Process all indices
        for part in parts[1:]:
            if part.endswith(']'):
                try:
                    index = int(part[:-1])
                    result.append(index)
                except ValueError:
                    # If not a valid integer index, treat as a string
                    result.append(part[:-1])

        return result

    def _traverse_path(self, obj: Any, path: List[Union[str, int]]) -> Any:
        """
        Traverse a path in an object.

        Args:
            obj: Object to traverse
            path: List of path components

        Returns:
            Value at the specified path

        Raises:
            KeyError: If a dictionary key is not found
            IndexError: If an array index is out of bounds
            TypeError: If an operation is not supported on the object
        """
        result = obj
        current_path = []

        for component in path:
            current_path.append(component)
            path_str = '.'.join(str(p) for p in current_path)

            try:
                # Handle standardized output format
                if component == "data" and isinstance(result, dict) and "data" in result and "status" in result and "tool_name" in result:
                    result = result["data"]
                    continue

                if component == "metadata" and isinstance(result, dict) and "metadata" in result and "status" in result and "tool_name" in result:
                    result = result["metadata"]
                    continue

                # Handle dictionary access
                if isinstance(result, dict):
                    if component in result:
                        result = result[component]
                    else:
                        raise KeyError(f"Key '{component}' not found in dictionary at path '{path_str}'")
                # Handle list/array access
                elif isinstance(result, list):
                    if isinstance(component, int):
                        if 0 <= component < len(result):
                            result = result[component]
                        else:
                            raise IndexError(f"Index {component} out of bounds for list of length {len(result)} at path '{path_str}'")
                    else:
                        # Try to access a property of each item in the list
                        if all(isinstance(item, dict) and component in item for item in result):
                            result = [item[component] for item in result]
                        else:
                            raise TypeError(f"Cannot access property '{component}' on all items in list at path '{path_str}'")
                # Handle string access (for JSON strings)
                elif isinstance(result, str) and component == "json":
                    try:
                        result = json.loads(result)
                    except json.JSONDecodeError:
                        raise ValueError(f"Cannot parse string as JSON at path '{path_str}': {result[:50]}...")
                else:
                    raise TypeError(f"Cannot access {component} in {type(result).__name__} at path '{path_str}'")
            except (KeyError, IndexError, TypeError, ValueError) as e:
                logger.warning(f"Error traversing path '{path_str}': {str(e)}")
                raise

        return result
